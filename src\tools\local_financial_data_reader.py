#!/usr/bin/env python3
"""
本地财务数据读取器
用于读取通过download_financial_data.py脚本下载的离线财务数据

支持的数据类型：
- 股价数据 (prices)
- 财务指标数据 (financial_metrics) 
- 财务科目数据 (line_items)
- 内幕交易数据 (insider_trades)
- 公司新闻数据 (company_news)
- 市值数据 (market_cap)
"""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import pandas as pd

logger = logging.getLogger(__name__)


class LocalFinancialDataReader:
    """本地财务数据读取器"""
    
    def __init__(self, base_dir: str = "financial_data_offline"):
        self.base_dir = Path(base_dir)
        if not self.base_dir.exists():
            logger.warning(f"本地财务数据目录不存在: {self.base_dir}")
    
    def get_data_directory(self, ticker: str, data_type: str) -> Path:
        """获取数据存储目录"""
        return self.base_dir / f"{ticker}_{data_type}"
    
    def find_closest_data_file(self, ticker: str, data_type: str, target_date: str) -> Optional[Path]:
        """查找最接近目标日期的数据文件"""
        data_dir = self.get_data_directory(ticker, data_type)
        
        if not data_dir.exists():
            return None
        
        target_dt = datetime.strptime(target_date, "%Y-%m-%d")
        best_file = None
        min_diff = float('inf')
        
        # 遍历目录中的所有JSON文件
        for file_path in data_dir.glob("*.json"):
            try:
                # 从文件名提取日期
                filename = file_path.stem
                if "_to_" in filename:
                    # 处理日期范围文件 (如股价数据)
                    continue
                
                # 提取日期部分
                date_part = filename.split("_")[-1]
                if len(date_part) == 10 and date_part.count("-") == 2:
                    file_dt = datetime.strptime(date_part, "%Y-%m-%d")
                    
                    # 只考虑目标日期之前或当天的数据
                    if file_dt <= target_dt:
                        diff = (target_dt - file_dt).days
                        if diff < min_diff:
                            min_diff = diff
                            best_file = file_path
            except (ValueError, IndexError):
                continue
        
        return best_file
    
    def load_data_from_file(self, file_path: Path) -> Optional[Any]:
        """从文件加载数据"""
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            return saved_data.get("data")
        except Exception as e:
            logger.error(f"加载数据失败 {file_path}: {e}")
            return None
    
    def get_prices_data(self, ticker: str, start_date: str, end_date: str) -> Optional[Any]:
        """获取股价数据"""
        data_dir = self.get_data_directory(ticker, "prices")
        
        if not data_dir.exists():
            logger.warning(f"股价数据目录不存在: {data_dir}")
            return None
        
        # 查找包含目标日期范围的文件
        for file_path in data_dir.glob("*.json"):
            try:
                filename = file_path.stem
                if "_to_" in filename:
                    # 解析日期范围
                    parts = filename.split("_")
                    if len(parts) >= 4:
                        file_start = parts[-3]
                        file_end = parts[-1]
                        
                        # 检查日期范围是否覆盖目标范围
                        if (file_start <= start_date and file_end >= end_date):
                            data = self.load_data_from_file(file_path)
                            if data:
                                logger.info(f"从本地文件加载股价数据: {ticker} ({start_date} to {end_date})")
                                return data
            except Exception as e:
                logger.debug(f"解析股价文件失败 {file_path}: {e}")
                continue
        
        logger.warning(f"未找到股价数据: {ticker} ({start_date} to {end_date})")
        return None
    
    def get_financial_metrics_data(self, ticker: str, end_date: str) -> Optional[Any]:
        """获取财务指标数据"""
        file_path = self.find_closest_data_file(ticker, "financial_metrics", end_date)
        
        if file_path:
            data = self.load_data_from_file(file_path)
            if data:
                logger.info(f"从本地文件加载财务指标数据: {ticker} ({end_date}) - 使用文件: {file_path.name}")
                return data
        
        logger.warning(f"未找到财务指标数据: {ticker} ({end_date})")
        return None
    
    def get_insider_trades_data(self, ticker: str, end_date: str) -> Optional[Any]:
        """获取内幕交易数据"""
        file_path = self.find_closest_data_file(ticker, "insider_trades", end_date)
        
        if file_path:
            data = self.load_data_from_file(file_path)
            if data:
                logger.info(f"从本地文件加载内幕交易数据: {ticker} ({end_date}) - 使用文件: {file_path.name}")
                return data
        
        logger.warning(f"未找到内幕交易数据: {ticker} ({end_date})")
        return None
    
    def get_company_news_data(self, ticker: str, end_date: str) -> Optional[Any]:
        """获取公司新闻数据"""
        file_path = self.find_closest_data_file(ticker, "company_news", end_date)
        
        if file_path:
            data = self.load_data_from_file(file_path)
            if data:
                logger.info(f"从本地文件加载公司新闻数据: {ticker} ({end_date}) - 使用文件: {file_path.name}")
                return data
        
        logger.warning(f"未找到公司新闻数据: {ticker} ({end_date})")
        return None
    
    def get_market_cap_data(self, ticker: str, date: str) -> Optional[Any]:
        """获取市值数据"""
        file_path = self.find_closest_data_file(ticker, "market_cap", date)
        
        if file_path:
            data = self.load_data_from_file(file_path)
            if data:
                logger.info(f"从本地文件加载市值数据: {ticker} ({date}) - 使用文件: {file_path.name}")
                return data
        
        logger.warning(f"未找到市值数据: {ticker} ({date})")
        return None
    
    def get_line_items_data(self, ticker: str, end_date: str) -> Optional[Any]:
        """获取财务科目数据"""
        file_path = self.find_closest_data_file(ticker, "line_items", end_date)
        
        if file_path:
            data = self.load_data_from_file(file_path)
            if data:
                logger.info(f"从本地文件加载财务科目数据: {ticker} ({end_date}) - 使用文件: {file_path.name}")
                return data
        
        logger.warning(f"未找到财务科目数据: {ticker} ({end_date})")
        return None
    
    def check_data_availability(self, ticker: str, start_date: str, end_date: str) -> Dict[str, bool]:
        """检查数据可用性"""
        availability = {}
        
        data_types = ["prices", "financial_metrics", "insider_trades", 
                     "company_news", "market_cap", "line_items"]
        
        for data_type in data_types:
            data_dir = self.get_data_directory(ticker, data_type)
            availability[data_type] = data_dir.exists() and len(list(data_dir.glob("*.json"))) > 0
        
        return availability
    
    def get_available_date_range(self, ticker: str, data_type: str) -> Optional[Dict[str, str]]:
        """获取可用数据的日期范围"""
        data_dir = self.get_data_directory(ticker, data_type)
        
        if not data_dir.exists():
            return None
        
        dates = []
        for file_path in data_dir.glob("*.json"):
            try:
                filename = file_path.stem
                if "_to_" in filename:
                    # 处理日期范围文件
                    parts = filename.split("_")
                    if len(parts) >= 4:
                        dates.extend([parts[-3], parts[-1]])
                else:
                    # 处理单日期文件
                    date_part = filename.split("_")[-1]
                    if len(date_part) == 10 and date_part.count("-") == 2:
                        dates.append(date_part)
            except Exception:
                continue
        
        if dates:
            dates.sort()
            return {"start_date": dates[0], "end_date": dates[-1]}
        
        return None


# 全局实例
_local_reader = None

def get_local_financial_data_reader(base_dir: str = "financial_data_offline") -> LocalFinancialDataReader:
    """获取本地财务数据读取器实例"""
    global _local_reader
    if _local_reader is None or _local_reader.base_dir != Path(base_dir):
        _local_reader = LocalFinancialDataReader(base_dir)
    return _local_reader


# 便捷函数
def load_local_prices(ticker: str, start_date: str, end_date: str, base_dir: str = "financial_data_offline") -> Optional[Any]:
    """加载本地股价数据"""
    reader = get_local_financial_data_reader(base_dir)
    return reader.get_prices_data(ticker, start_date, end_date)


def load_local_financial_metrics(ticker: str, end_date: str, base_dir: str = "financial_data_offline") -> Optional[Any]:
    """加载本地财务指标数据"""
    reader = get_local_financial_data_reader(base_dir)
    return reader.get_financial_metrics_data(ticker, end_date)


def load_local_insider_trades(ticker: str, end_date: str, base_dir: str = "financial_data_offline") -> Optional[Any]:
    """加载本地内幕交易数据"""
    reader = get_local_financial_data_reader(base_dir)
    return reader.get_insider_trades_data(ticker, end_date)


def load_local_company_news(ticker: str, end_date: str, base_dir: str = "financial_data_offline") -> Optional[Any]:
    """加载本地公司新闻数据"""
    reader = get_local_financial_data_reader(base_dir)
    return reader.get_company_news_data(ticker, end_date)


def load_local_market_cap(ticker: str, date: str, base_dir: str = "financial_data_offline") -> Optional[Any]:
    """加载本地市值数据"""
    reader = get_local_financial_data_reader(base_dir)
    return reader.get_market_cap_data(ticker, date)


def load_local_line_items(ticker: str, end_date: str, base_dir: str = "financial_data_offline") -> Optional[Any]:
    """加载本地财务科目数据"""
    reader = get_local_financial_data_reader(base_dir)
    return reader.get_line_items_data(ticker, end_date)
