#!/usr/bin/env python3
"""
财务数据配置管理
支持本地财务数据源选择和配置
"""

import argparse
import os
from typing import List, Optional, Dict, Any
from pathlib import Path


class FinancialDataConfig:
    """财务数据配置管理器"""
    
    def __init__(self):
        self.config = {
            # 本地财务数据配置
            "use_local_financial_data": False,
            "local_data_directory": "financial_data_offline",
            
            # 支持的数据类型
            "supported_data_types": [
                "prices",
                "financial_metrics", 
                "insider_trades",
                "company_news",
                "market_cap",
                "line_items"
            ],
            
            # 数据源优先级
            "data_source_priority": [
                "local",  # 本地数据优先
                "api"     # API数据作为回退
            ],
            
            # 本地数据回退策略
            "fallback_strategy": {
                "max_days_back": 30,  # 最多向前查找30天的数据
                "allow_future_data": False,  # 不允许使用未来数据
                "require_exact_date": False  # 不要求精确日期匹配
            },
            
            # API配置
            "api_config": {
                "rate_limit_delay": 0.5,
                "max_retries": 3,
                "timeout": 30
            }
        }
    
    def set_use_local_data(self, use_local: bool):
        """设置是否使用本地数据"""
        self.config["use_local_financial_data"] = use_local
    
    def should_use_local_data(self) -> bool:
        """检查是否应该使用本地数据"""
        return self.config["use_local_financial_data"]
    
    def set_local_data_directory(self, directory: str):
        """设置本地数据目录"""
        self.config["local_data_directory"] = directory
    
    def get_local_data_directory(self) -> str:
        """获取本地数据目录"""
        return self.config["local_data_directory"]
    
    def get_data_directory_path(self, ticker: str, data_type: str) -> Path:
        """获取特定股票和数据类型的目录路径"""
        base_dir = Path(self.config["local_data_directory"])
        return base_dir / f"{ticker}_{data_type}"
    
    def check_local_data_availability(self, ticker: str, data_type: str) -> bool:
        """检查本地数据是否可用"""
        data_dir = self.get_data_directory_path(ticker, data_type)
        return data_dir.exists() and len(list(data_dir.glob("*.json"))) > 0
    
    def get_available_tickers(self) -> List[str]:
        """获取有本地数据的股票代码列表"""
        base_dir = Path(self.config["local_data_directory"])
        if not base_dir.exists():
            return []
        
        tickers = set()
        for item in base_dir.iterdir():
            if item.is_dir():
                # 解析目录名格式: TICKER_datatype
                parts = item.name.split("_")
                if len(parts) >= 2:
                    ticker = parts[0]
                    tickers.add(ticker)
        
        return sorted(list(tickers))
    
    def get_data_coverage_report(self) -> Dict[str, Dict[str, Any]]:
        """生成数据覆盖报告"""
        report = {}
        tickers = self.get_available_tickers()
        
        for ticker in tickers:
            ticker_report = {
                "data_types": {},
                "total_files": 0
            }
            
            for data_type in self.config["supported_data_types"]:
                data_dir = self.get_data_directory_path(ticker, data_type)
                
                if data_dir.exists():
                    json_files = list(data_dir.glob("*.json"))
                    file_count = len(json_files)
                    
                    # 获取日期范围
                    dates = []
                    for file_path in json_files:
                        try:
                            filename = file_path.stem
                            if "_to_" in filename:
                                # 处理日期范围文件
                                parts = filename.split("_")
                                if len(parts) >= 4:
                                    dates.extend([parts[-3], parts[-1]])
                            else:
                                # 处理单日期文件
                                date_part = filename.split("_")[-1]
                                if len(date_part) == 10 and date_part.count("-") == 2:
                                    dates.append(date_part)
                        except Exception:
                            continue
                    
                    date_range = None
                    if dates:
                        dates.sort()
                        date_range = {"start": dates[0], "end": dates[-1]}
                    
                    ticker_report["data_types"][data_type] = {
                        "available": True,
                        "file_count": file_count,
                        "date_range": date_range
                    }
                    ticker_report["total_files"] += file_count
                else:
                    ticker_report["data_types"][data_type] = {
                        "available": False,
                        "file_count": 0,
                        "date_range": None
                    }
            
            report[ticker] = ticker_report
        
        return report
    
    def validate_configuration(self) -> Dict[str, Any]:
        """验证配置有效性"""
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 检查本地数据目录
        if self.should_use_local_data():
            local_dir = Path(self.config["local_data_directory"])
            if not local_dir.exists():
                validation_result["errors"].append(f"本地数据目录不存在: {local_dir}")
                validation_result["valid"] = False
            elif not any(local_dir.iterdir()):
                validation_result["warnings"].append(f"本地数据目录为空: {local_dir}")
        
        # 检查API密钥
        api_key = os.getenv('FINANCIAL_DATASETS_API_KEY')
        if not api_key and not self.should_use_local_data():
            validation_result["errors"].append("FINANCIAL_DATASETS_API_KEY 环境变量未设置，且未启用本地数据")
            validation_result["valid"] = False
        
        return validation_result
    
    def print_configuration(self):
        """打印当前配置"""
        print("=" * 50)
        print("财务数据配置")
        print("=" * 50)
        print(f"使用本地数据: {'是' if self.should_use_local_data() else '否'}")
        print(f"本地数据目录: {self.config['local_data_directory']}")
        
        if self.should_use_local_data():
            # 显示数据覆盖情况
            tickers = self.get_available_tickers()
            print(f"可用股票代码: {', '.join(tickers) if tickers else '无'}")
            
            if tickers:
                print("\n数据类型覆盖:")
                for ticker in tickers[:3]:  # 只显示前3个股票的详情
                    print(f"  {ticker}:")
                    for data_type in self.config["supported_data_types"]:
                        available = self.check_local_data_availability(ticker, data_type)
                        status = "✓" if available else "✗"
                        print(f"    {status} {data_type}")
                
                if len(tickers) > 3:
                    print(f"  ... 还有 {len(tickers) - 3} 个股票")
        
        print("=" * 50)


# 全局配置实例
_financial_data_config = FinancialDataConfig()


def get_financial_data_config() -> FinancialDataConfig:
    """获取财务数据配置实例"""
    return _financial_data_config


def setup_financial_data_config_from_args(args):
    """从命令行参数设置财务数据配置"""
    config = get_financial_data_config()
    
    if hasattr(args, 'use_local_financial_data') and args.use_local_financial_data:
        config.set_use_local_data(True)
    
    if hasattr(args, 'financial_data_dir') and args.financial_data_dir:
        config.set_local_data_directory(args.financial_data_dir)


def add_financial_data_config_args(parser: argparse.ArgumentParser):
    """添加财务数据配置相关的命令行参数"""
    financial_group = parser.add_argument_group('财务数据配置')
    
    financial_group.add_argument(
        "--use-local-financial-data",
        action="store_true",
        help="使用本地财务数据而不是API调用"
    )
    
    financial_group.add_argument(
        "--financial-data-dir",
        type=str,
        default="financial_data_offline",
        help="本地财务数据目录路径 (默认: financial_data_offline)"
    )


def print_financial_data_config():
    """打印财务数据配置信息"""
    config = get_financial_data_config()
    config.print_configuration()


def validate_financial_data_config() -> bool:
    """验证财务数据配置"""
    config = get_financial_data_config()
    validation_result = config.validate_configuration()
    
    if validation_result["errors"]:
        print("财务数据配置错误:")
        for error in validation_result["errors"]:
            print(f"  ❌ {error}")
    
    if validation_result["warnings"]:
        print("财务数据配置警告:")
        for warning in validation_result["warnings"]:
            print(f"  ⚠️ {warning}")
    
    return validation_result["valid"]


# 便捷函数
def should_use_local_financial_data() -> bool:
    """检查是否应该使用本地财务数据"""
    return get_financial_data_config().should_use_local_data()


def get_local_financial_data_directory() -> str:
    """获取本地财务数据目录"""
    return get_financial_data_config().get_local_data_directory()


def check_local_financial_data_availability(ticker: str, data_type: str) -> bool:
    """检查本地财务数据是否可用"""
    return get_financial_data_config().check_local_data_availability(ticker, data_type)
