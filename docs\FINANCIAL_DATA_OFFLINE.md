# 财务数据离线保存系统

## 概述

财务数据离线保存系统允许您将依赖 `FINANCIAL_DATASETS_API_KEY` 的所有代理数据本地化存储，减少对外部API的依赖，提高回测性能和可靠性。

## 支持的数据类型

- **股价数据** (`prices`) - 日线价格、成交量数据
- **财务指标数据** (`financial_metrics`) - TTM财务指标
- **财务科目数据** (`line_items`) - 特定财务科目搜索
- **内幕交易数据** (`insider_trades`) - 内幕人员交易记录
- **公司新闻数据** (`company_news`) - 公司相关新闻
- **市值数据** (`market_cap`) - 实时市值信息

## 依赖此系统的代理

约15个代理依赖 `FINANCIAL_DATASETS_API_KEY`：
- `fundamentals_analyst_agent`
- `warren_buffett_agent`
- `peter_lynch_agent`
- `ben_graham_agent`
- `charlie_munger_agent`
- `michael_burry_agent`
- `bill_ackman_agent`
- `cathie_wood_agent`
- `stanley_druckenmiller_agent`
- `aswath_damodaran_agent`
- `phil_fisher_agent`
- `technicals_agent`
- `news_analyst_agent`
- 等等...

## 快速开始

### 1. 下载财务数据

```bash
# 下载AAPL, MSFT, NVDA的所有数据类型（2025年1-6月）
python scripts/download_financial_data.py \
    --tickers AAPL MSFT NVDA \
    --start-date 2025-01-01 \
    --end-date 2025-06-01

# 只下载特定数据类型
python scripts/download_financial_data.py \
    --tickers AAPL \
    --start-date 2025-01-01 \
    --end-date 2025-03-01 \
    --data-types prices financial_metrics

# 使用自定义设置
python scripts/download_financial_data.py \
    --tickers NVDA \
    --start-date 2025-01-01 \
    --end-date 2025-02-01 \
    --output-dir my_financial_data \
    --max-workers 5 \
    --rate-limit 1.0
```

### 2. 验证下载的数据

```bash
# 验证数据完整性
python scripts/download_financial_data.py \
    --tickers AAPL MSFT NVDA \
    --validate-only
```

### 3. 在回测中使用本地数据

```bash
# 启用本地财务数据模式进行回测
python src/backtester.py \
    --tickers NVDA \
    --start-date 2025-01-02 \
    --end-date 2025-06-01 \
    --use-local-financial-data \
    --financial-data-dir financial_data_offline
```

## 目录结构

下载的数据将按以下结构组织：

```
financial_data_offline/
├── AAPL_prices/
│   └── AAPL_prices_2025-01-01_to_2025-06-01.json
├── AAPL_financial_metrics/
│   ├── AAPL_financial_metrics_2025-01-01.json
│   ├── AAPL_financial_metrics_2025-01-08.json
│   └── ...
├── AAPL_insider_trades/
│   ├── AAPL_insider_trades_2025-01-01.json
│   └── ...
├── AAPL_company_news/
│   ├── AAPL_company_news_2025-01-01.json
│   └── ...
├── AAPL_market_cap/
│   ├── AAPL_market_cap_2025-01-01.json
│   └── ...
├── AAPL_line_items/
│   ├── AAPL_line_items_2025-01-01.json
│   └── ...
├── MSFT_prices/
├── MSFT_financial_metrics/
├── ...
└── download_report_20250107_143022.txt
```

## 数据文件格式

每个JSON文件包含以下结构：

```json
{
  "data": {
    // 实际的API响应数据
  },
  "metadata": {
    "download_time": "2025-01-07T14:30:22.123456",
    "api_source": "FINANCIAL_DATASETS_API",
    "ticker": "AAPL",
    "end_date": "2025-01-01",
    "data_type": "financial_metrics"
  }
}
```

## 命令行参数

### download_financial_data.py

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--tickers` | 股票代码列表 | 必需 |
| `--start-date` | 开始日期 (YYYY-MM-DD) | 必需 |
| `--end-date` | 结束日期 (YYYY-MM-DD) | 必需 |
| `--data-types` | 数据类型选择 | 所有类型 |
| `--output-dir` | 输出目录 | `financial_data_offline` |
| `--rate-limit` | API调用间隔(秒) | `0.5` |
| `--max-workers` | 最大并发数 | `3` |
| `--validate-only` | 仅验证数据 | `False` |
| `--force-redownload` | 强制重新下载 | `False` |
| `--verbose` | 详细日志 | `False` |

### backtester.py (新增参数)

| 参数 | 描述 | 默认值 |
|------|------|--------|
| `--use-local-financial-data` | 使用本地财务数据 | `False` |
| `--financial-data-dir` | 本地财务数据目录 | `financial_data_offline` |

## 数据回退机制

系统实现了智能的数据回退机制：

1. **本地数据优先**: 如果启用本地模式且数据可用，优先使用本地数据
2. **API回退**: 如果本地数据不可用，自动回退到API调用
3. **日期匹配**: 自动查找最接近目标日期的历史数据
4. **错误处理**: 优雅处理数据缺失和加载错误

## 性能优化

### 下载优化
- **并发下载**: 使用线程池并行下载多个股票
- **频率限制**: 可配置的API调用间隔避免超限
- **断点续传**: 跳过已存在的文件，支持增量更新
- **批量处理**: 股价数据按月下载，其他数据按周采样

### 回测优化
- **缓存集成**: 与现有缓存系统兼容
- **智能加载**: 只在需要时加载数据文件
- **内存管理**: 避免同时加载大量数据文件

## 监控和报告

### 下载报告
脚本会生成详细的下载报告，包括：
- 总请求数和成功率
- 按股票的详细结果
- 失败任务列表
- 生成的目录结构

### 数据验证
- 文件完整性检查
- JSON格式验证
- 数据可用性统计
- 覆盖范围报告

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   错误: FINANCIAL_DATASETS_API_KEY 环境变量未设置
   解决: 确保.env文件中设置了正确的API密钥
   ```

2. **网络超时**
   ```
   解决: 增加--rate-limit参数值，减少并发数--max-workers
   ```

3. **磁盘空间不足**
   ```
   解决: 检查可用磁盘空间，5个月数据约需要几GB空间
   ```

4. **数据不完整**
   ```
   解决: 使用--validate-only检查，然后重新下载缺失数据
   ```

### 日志分析

查看详细日志：
```bash
# 查看下载日志
tail -f financial_data_offline/download.log

# 启用详细模式
python scripts/download_financial_data.py --verbose ...
```

## 最佳实践

1. **分批下载**: 对于大量股票，分批下载避免超时
2. **定期更新**: 定期运行脚本更新数据
3. **备份数据**: 定期备份下载的数据文件
4. **监控空间**: 监控磁盘空间使用情况
5. **验证数据**: 定期验证数据完整性

## 扩展支持

### 添加新股票
```bash
# 为新股票下载数据
python scripts/download_financial_data.py \
    --tickers TSLA GOOGL \
    --start-date 2025-01-01 \
    --end-date 2025-06-01
```

### 更新现有数据
```bash
# 增量更新（跳过已存在文件）
python scripts/download_financial_data.py \
    --tickers AAPL MSFT NVDA \
    --start-date 2025-06-01 \
    --end-date 2025-07-01

# 强制重新下载
python scripts/download_financial_data.py \
    --tickers AAPL \
    --start-date 2025-01-01 \
    --end-date 2025-02-01 \
    --force-redownload
```

## 技术架构

- **下载器**: `scripts/download_financial_data.py`
- **读取器**: `src/tools/local_financial_data_reader.py`
- **配置管理**: `src/config/financial_data_config.py`
- **API增强**: `src/tools/api_with_local_fallback.py`

这个系统为AI对冲基金回测提供了可靠的离线数据支持，显著减少了对外部API的依赖。
