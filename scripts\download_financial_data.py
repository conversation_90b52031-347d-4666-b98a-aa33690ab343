#!/usr/bin/env python3
"""
财务数据离线保存脚本
用于将依赖FINANCIAL_DATASETS_API_KEY的所有代理数据本地化存储

支持的数据类型：
- 股价数据 (get_prices)
- 财务指标数据 (get_financial_metrics) 
- 财务科目数据 (search_line_items)
- 内幕交易数据 (get_insider_trades)
- 公司新闻数据 (get_company_news)
- 市值数据 (get_market_cap)

使用方法：
python scripts/download_financial_data.py --tickers AAPL MSFT NVDA --start-date 2025-01-01 --end-date 2025-06-01
"""

import os
import sys
import json
import argparse
import time
import requests
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入项目模块
try:
    from src.tools.api import (
        get_prices, get_financial_metrics, search_line_items,
        get_insider_trades, get_company_news, get_market_cap
    )
    from src.data.cache import Cache
    from dotenv import load_dotenv
    load_dotenv()
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


@dataclass
class DownloadStats:
    """下载统计信息"""
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    cached_requests: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    
    def add_success(self):
        self.successful_requests += 1
        self.total_requests += 1
    
    def add_failure(self):
        self.failed_requests += 1
        self.total_requests += 1
    
    def add_cached(self):
        self.cached_requests += 1
        self.total_requests += 1
    
    def get_success_rate(self) -> float:
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100
    
    def get_duration(self) -> timedelta:
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return timedelta(0)


class FinancialDataDownloader:
    """财务数据下载器"""
    
    def __init__(self, base_dir: str = "financial_data_offline", 
                 rate_limit_delay: float = 0.5, max_workers: int = 3):
        self.base_dir = Path(base_dir)
        self.rate_limit_delay = rate_limit_delay
        self.max_workers = max_workers
        self.stats = DownloadStats()
        self.lock = threading.Lock()
        
        # 创建基础目录
        self.base_dir.mkdir(exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
        # 验证API密钥
        self.api_key = os.getenv('FINANCIAL_DATASETS_API_KEY')
        if not self.api_key:
            raise ValueError("FINANCIAL_DATASETS_API_KEY 环境变量未设置")
        
        # 初始化缓存
        try:
            self.cache = Cache()
        except Exception as e:
            self.logger.warning(f"缓存初始化失败: {e}")
            self.cache = None
    
    def setup_logging(self):
        """设置日志记录"""
        log_file = self.base_dir / "download.log"
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_data_directory(self, ticker: str, data_type: str) -> Path:
        """获取数据存储目录"""
        data_dir = self.base_dir / f"{ticker}_{data_type}"
        data_dir.mkdir(exist_ok=True)
        return data_dir
    
    def get_data_file_path(self, ticker: str, data_type: str, date: str) -> Path:
        """获取数据文件路径"""
        data_dir = self.get_data_directory(ticker, data_type)
        return data_dir / f"{ticker}_{data_type}_{date}.json"
    
    def save_data_to_file(self, data: Any, file_path: Path, metadata: Dict = None):
        """保存数据到文件"""
        try:
            # 准备保存的数据结构
            save_data = {
                "data": data,
                "metadata": {
                    "download_time": datetime.now().isoformat(),
                    "api_source": "FINANCIAL_DATASETS_API",
                    **(metadata or {})
                }
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False, default=str)
            
            self.logger.debug(f"数据已保存到: {file_path}")
            return True
        except Exception as e:
            self.logger.error(f"保存数据失败 {file_path}: {e}")
            return False
    
    def load_data_from_file(self, file_path: Path) -> Optional[Any]:
        """从文件加载数据"""
        try:
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            return saved_data.get("data")
        except Exception as e:
            self.logger.error(f"加载数据失败 {file_path}: {e}")
            return None
    
    def rate_limit_wait(self):
        """API调用频率限制"""
        time.sleep(self.rate_limit_delay)
    
    def download_prices_data(self, ticker: str, start_date: str, end_date: str) -> bool:
        """下载股价数据"""
        try:
            file_path = self.get_data_file_path(ticker, "prices", f"{start_date}_to_{end_date}")
            
            # 检查是否已存在
            if file_path.exists():
                self.logger.info(f"股价数据已存在: {ticker} ({start_date} to {end_date})")
                with self.lock:
                    self.stats.add_cached()
                return True
            
            self.logger.info(f"下载股价数据: {ticker} ({start_date} to {end_date})")
            self.rate_limit_wait()
            
            # 调用API获取数据
            data = get_prices(
                ticker=ticker,
                start_date=start_date,
                end_date=end_date,
                agent_name="financial_data_downloader"
            )
            
            if data:
                metadata = {
                    "ticker": ticker,
                    "start_date": start_date,
                    "end_date": end_date,
                    "data_type": "prices"
                }
                
                if self.save_data_to_file(data, file_path, metadata):
                    with self.lock:
                        self.stats.add_success()
                    return True
            
            with self.lock:
                self.stats.add_failure()
            return False
            
        except Exception as e:
            self.logger.error(f"下载股价数据失败 {ticker}: {e}")
            with self.lock:
                self.stats.add_failure()
            return False
    
    def download_financial_metrics_data(self, ticker: str, end_date: str) -> bool:
        """下载财务指标数据"""
        try:
            file_path = self.get_data_file_path(ticker, "financial_metrics", end_date)
            
            # 检查是否已存在
            if file_path.exists():
                self.logger.info(f"财务指标数据已存在: {ticker} ({end_date})")
                with self.lock:
                    self.stats.add_cached()
                return True
            
            self.logger.info(f"下载财务指标数据: {ticker} ({end_date})")
            self.rate_limit_wait()
            
            # 调用API获取数据
            data = get_financial_metrics(
                ticker=ticker,
                end_date=end_date,
                period="ttm",
                limit=10,
                agent_name="financial_data_downloader"
            )
            
            if data:
                metadata = {
                    "ticker": ticker,
                    "end_date": end_date,
                    "period": "ttm",
                    "data_type": "financial_metrics"
                }
                
                if self.save_data_to_file(data, file_path, metadata):
                    with self.lock:
                        self.stats.add_success()
                    return True
            
            with self.lock:
                self.stats.add_failure()
            return False
            
        except Exception as e:
            self.logger.error(f"下载财务指标数据失败 {ticker}: {e}")
            with self.lock:
                self.stats.add_failure()
            return False

    def download_insider_trades_data(self, ticker: str, end_date: str) -> bool:
        """下载内幕交易数据"""
        try:
            file_path = self.get_data_file_path(ticker, "insider_trades", end_date)

            # 检查是否已存在
            if file_path.exists():
                self.logger.info(f"内幕交易数据已存在: {ticker} ({end_date})")
                with self.lock:
                    self.stats.add_cached()
                return True

            self.logger.info(f"下载内幕交易数据: {ticker} ({end_date})")
            self.rate_limit_wait()

            # 调用API获取数据
            data = get_insider_trades(
                ticker=ticker,
                end_date=end_date,
                limit=50,
                agent_name="financial_data_downloader"
            )

            if data:
                metadata = {
                    "ticker": ticker,
                    "end_date": end_date,
                    "data_type": "insider_trades"
                }

                if self.save_data_to_file(data, file_path, metadata):
                    with self.lock:
                        self.stats.add_success()
                    return True

            with self.lock:
                self.stats.add_failure()
            return False

        except Exception as e:
            self.logger.error(f"下载内幕交易数据失败 {ticker}: {e}")
            with self.lock:
                self.stats.add_failure()
            return False

    def download_company_news_data(self, ticker: str, end_date: str) -> bool:
        """下载公司新闻数据"""
        try:
            file_path = self.get_data_file_path(ticker, "company_news", end_date)

            # 检查是否已存在
            if file_path.exists():
                self.logger.info(f"公司新闻数据已存在: {ticker} ({end_date})")
                with self.lock:
                    self.stats.add_cached()
                return True

            self.logger.info(f"下载公司新闻数据: {ticker} ({end_date})")
            self.rate_limit_wait()

            # 调用API获取数据
            data = get_company_news(
                ticker=ticker,
                end_date=end_date,
                limit=100,
                agent_name="financial_data_downloader"
            )

            if data:
                metadata = {
                    "ticker": ticker,
                    "end_date": end_date,
                    "data_type": "company_news"
                }

                if self.save_data_to_file(data, file_path, metadata):
                    with self.lock:
                        self.stats.add_success()
                    return True

            with self.lock:
                self.stats.add_failure()
            return False

        except Exception as e:
            self.logger.error(f"下载公司新闻数据失败 {ticker}: {e}")
            with self.lock:
                self.stats.add_failure()
            return False

    def download_market_cap_data(self, ticker: str, date: str) -> bool:
        """下载市值数据"""
        try:
            file_path = self.get_data_file_path(ticker, "market_cap", date)

            # 检查是否已存在
            if file_path.exists():
                self.logger.info(f"市值数据已存在: {ticker} ({date})")
                with self.lock:
                    self.stats.add_cached()
                return True

            self.logger.info(f"下载市值数据: {ticker} ({date})")
            self.rate_limit_wait()

            # 调用API获取数据
            data = get_market_cap(
                ticker=ticker,
                end_date=date
            )

            if data:
                metadata = {
                    "ticker": ticker,
                    "date": date,
                    "data_type": "market_cap"
                }

                if self.save_data_to_file(data, file_path, metadata):
                    with self.lock:
                        self.stats.add_success()
                    return True

            with self.lock:
                self.stats.add_failure()
            return False

        except Exception as e:
            self.logger.error(f"下载市值数据失败 {ticker}: {e}")
            with self.lock:
                self.stats.add_failure()
            return False

    def download_line_items_data(self, ticker: str, end_date: str) -> bool:
        """下载财务科目数据"""
        try:
            file_path = self.get_data_file_path(ticker, "line_items", end_date)

            # 检查是否已存在
            if file_path.exists():
                self.logger.info(f"财务科目数据已存在: {ticker} ({end_date})")
                with self.lock:
                    self.stats.add_cached()
                return True

            self.logger.info(f"下载财务科目数据: {ticker} ({end_date})")
            self.rate_limit_wait()

            # 常用财务科目列表
            common_line_items = [
                "revenue", "net_income", "total_assets", "total_debt",
                "cash_and_equivalents", "operating_cash_flow", "free_cash_flow",
                "gross_profit", "operating_income", "ebitda", "total_equity",
                "current_assets", "current_liabilities", "long_term_debt",
                "research_and_development", "capital_expenditures"
            ]

            # 调用API获取数据
            data = search_line_items(
                ticker=ticker,
                line_items=common_line_items,
                end_date=end_date,
                period="ttm",
                agent_name="financial_data_downloader"
            )

            if data:
                metadata = {
                    "ticker": ticker,
                    "end_date": end_date,
                    "line_items": common_line_items,
                    "data_type": "line_items"
                }

                if self.save_data_to_file(data, file_path, metadata):
                    with self.lock:
                        self.stats.add_success()
                    return True

            with self.lock:
                self.stats.add_failure()
            return False

        except Exception as e:
            self.logger.error(f"下载财务科目数据失败 {ticker}: {e}")
            with self.lock:
                self.stats.add_failure()
            return False

    def download_ticker_data(self, ticker: str, start_date: str, end_date: str,
                           data_types: List[str]) -> Dict[str, bool]:
        """下载单个股票的所有数据类型"""
        results = {}

        # 生成日期列表（每周一次采样以减少API调用）
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")

        # 股价数据 - 按月下载以减少API调用
        if "prices" in data_types:
            results["prices"] = self.download_prices_data(ticker, start_date, end_date)

        # 其他数据类型 - 按周采样
        sample_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            sample_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=7)  # 每周采样

        # 确保包含结束日期
        if end_date not in sample_dates:
            sample_dates.append(end_date)

        for date in sample_dates:
            if "financial_metrics" in data_types:
                key = f"financial_metrics_{date}"
                results[key] = self.download_financial_metrics_data(ticker, date)

            if "insider_trades" in data_types:
                key = f"insider_trades_{date}"
                results[key] = self.download_insider_trades_data(ticker, date)

            if "company_news" in data_types:
                key = f"company_news_{date}"
                results[key] = self.download_company_news_data(ticker, date)

            if "market_cap" in data_types:
                key = f"market_cap_{date}"
                results[key] = self.download_market_cap_data(ticker, date)

            if "line_items" in data_types:
                key = f"line_items_{date}"
                results[key] = self.download_line_items_data(ticker, date)

        return results

    def download_all_data(self, tickers: List[str], start_date: str, end_date: str,
                         data_types: Optional[List[str]] = None) -> Dict[str, Dict[str, bool]]:
        """下载所有股票的数据"""
        if data_types is None:
            data_types = ["prices", "financial_metrics", "insider_trades",
                         "company_news", "market_cap", "line_items"]

        self.stats.start_time = datetime.now()
        self.logger.info(f"开始下载数据: {len(tickers)} 个股票, {len(data_types)} 种数据类型")
        self.logger.info(f"时间范围: {start_date} 到 {end_date}")
        self.logger.info(f"数据类型: {', '.join(data_types)}")

        all_results = {}

        # 使用线程池并行下载
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有下载任务
            future_to_ticker = {
                executor.submit(self.download_ticker_data, ticker, start_date, end_date, data_types): ticker
                for ticker in tickers
            }

            # 收集结果
            for future in as_completed(future_to_ticker):
                ticker = future_to_ticker[future]
                try:
                    results = future.result()
                    all_results[ticker] = results
                    self.logger.info(f"完成下载: {ticker}")
                except Exception as e:
                    self.logger.error(f"下载失败 {ticker}: {e}")
                    all_results[ticker] = {}

        self.stats.end_time = datetime.now()
        return all_results

    def generate_report(self, results: Dict[str, Dict[str, bool]]) -> str:
        """生成下载报告"""
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("财务数据下载报告")
        report_lines.append("=" * 60)

        # 统计信息
        report_lines.append(f"总请求数: {self.stats.total_requests}")
        report_lines.append(f"成功请求: {self.stats.successful_requests}")
        report_lines.append(f"失败请求: {self.stats.failed_requests}")
        report_lines.append(f"缓存命中: {self.stats.cached_requests}")
        report_lines.append(f"成功率: {self.stats.get_success_rate():.1f}%")
        report_lines.append(f"总耗时: {self.stats.get_duration()}")
        report_lines.append("")

        # 按股票详细结果
        for ticker, ticker_results in results.items():
            report_lines.append(f"股票: {ticker}")
            success_count = sum(1 for success in ticker_results.values() if success)
            total_count = len(ticker_results)
            report_lines.append(f"  成功: {success_count}/{total_count}")

            # 失败的任务
            failed_tasks = [task for task, success in ticker_results.items() if not success]
            if failed_tasks:
                report_lines.append(f"  失败任务: {', '.join(failed_tasks)}")
            report_lines.append("")

        # 目录结构
        report_lines.append("生成的目录结构:")
        for ticker in results.keys():
            report_lines.append(f"  {self.base_dir}/")
            for data_type in ["prices", "financial_metrics", "insider_trades",
                            "company_news", "market_cap", "line_items"]:
                data_dir = self.base_dir / f"{ticker}_{data_type}"
                if data_dir.exists():
                    file_count = len(list(data_dir.glob("*.json")))
                    report_lines.append(f"    {ticker}_{data_type}/ ({file_count} 文件)")

        report_lines.append("=" * 60)

        report_content = "\n".join(report_lines)

        # 保存报告到文件
        report_file = self.base_dir / f"download_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)

        self.logger.info(f"报告已保存到: {report_file}")
        return report_content

    def validate_data_integrity(self, tickers: List[str]) -> Dict[str, Dict[str, Any]]:
        """验证数据完整性"""
        validation_results = {}

        for ticker in tickers:
            ticker_validation = {
                "data_types": {},
                "total_files": 0,
                "valid_files": 0,
                "invalid_files": 0
            }

            for data_type in ["prices", "financial_metrics", "insider_trades",
                            "company_news", "market_cap", "line_items"]:
                data_dir = self.base_dir / f"{ticker}_{data_type}"

                if data_dir.exists():
                    json_files = list(data_dir.glob("*.json"))
                    valid_count = 0

                    for file_path in json_files:
                        try:
                            data = self.load_data_from_file(file_path)
                            if data is not None:
                                valid_count += 1
                            ticker_validation["total_files"] += 1
                        except Exception:
                            ticker_validation["invalid_files"] += 1

                    ticker_validation["data_types"][data_type] = {
                        "total_files": len(json_files),
                        "valid_files": valid_count,
                        "directory": str(data_dir)
                    }
                    ticker_validation["valid_files"] += valid_count

            validation_results[ticker] = ticker_validation

        return validation_results


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="财务数据离线保存脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 下载AAPL, MSFT, NVDA的所有数据类型
  python scripts/download_financial_data.py --tickers AAPL MSFT NVDA --start-date 2025-01-01 --end-date 2025-06-01

  # 只下载股价和财务指标数据
  python scripts/download_financial_data.py --tickers AAPL --start-date 2025-01-01 --end-date 2025-03-01 --data-types prices financial_metrics

  # 使用自定义目录和并发设置
  python scripts/download_financial_data.py --tickers NVDA --start-date 2025-01-01 --end-date 2025-02-01 --output-dir my_data --max-workers 5 --rate-limit 1.0
        """
    )

    parser.add_argument(
        "--tickers",
        nargs="+",
        required=True,
        help="股票代码列表，例如: AAPL MSFT NVDA"
    )

    parser.add_argument(
        "--start-date",
        required=True,
        help="开始日期 (YYYY-MM-DD 格式)，例如: 2025-01-01"
    )

    parser.add_argument(
        "--end-date",
        required=True,
        help="结束日期 (YYYY-MM-DD 格式)，例如: 2025-06-01"
    )

    parser.add_argument(
        "--data-types",
        nargs="+",
        choices=["prices", "financial_metrics", "insider_trades", "company_news", "market_cap", "line_items"],
        default=["prices", "financial_metrics", "insider_trades", "company_news", "market_cap", "line_items"],
        help="要下载的数据类型 (默认: 所有类型)"
    )

    parser.add_argument(
        "--output-dir",
        default="financial_data_offline",
        help="输出目录 (默认: financial_data_offline)"
    )

    parser.add_argument(
        "--rate-limit",
        type=float,
        default=0.5,
        help="API调用间隔秒数 (默认: 0.5)"
    )

    parser.add_argument(
        "--max-workers",
        type=int,
        default=3,
        help="最大并发线程数 (默认: 3)"
    )

    parser.add_argument(
        "--validate-only",
        action="store_true",
        help="仅验证现有数据完整性，不下载新数据"
    )

    parser.add_argument(
        "--force-redownload",
        action="store_true",
        help="强制重新下载已存在的数据文件"
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="显示详细日志信息"
    )

    return parser.parse_args()


def validate_date_format(date_str: str) -> bool:
    """验证日期格式"""
    try:
        datetime.strptime(date_str, "%Y-%m-%d")
        return True
    except ValueError:
        return False


def main():
    """主函数"""
    args = parse_arguments()

    # 验证日期格式
    if not validate_date_format(args.start_date):
        print(f"错误: 开始日期格式无效: {args.start_date}")
        print("请使用 YYYY-MM-DD 格式，例如: 2025-01-01")
        sys.exit(1)

    if not validate_date_format(args.end_date):
        print(f"错误: 结束日期格式无效: {args.end_date}")
        print("请使用 YYYY-MM-DD 格式，例如: 2025-06-01")
        sys.exit(1)

    # 验证日期范围
    start_dt = datetime.strptime(args.start_date, "%Y-%m-%d")
    end_dt = datetime.strptime(args.end_date, "%Y-%m-%d")

    if start_dt >= end_dt:
        print("错误: 开始日期必须早于结束日期")
        sys.exit(1)

    # 检查日期范围是否过大
    date_diff = end_dt - start_dt
    if date_diff.days > 365:
        print(f"警告: 日期范围较大 ({date_diff.days} 天)，下载可能需要较长时间")
        response = input("是否继续? (y/N): ")
        if response.lower() != 'y':
            print("下载已取消")
            sys.exit(0)

    try:
        # 创建下载器
        downloader = FinancialDataDownloader(
            base_dir=args.output_dir,
            rate_limit_delay=args.rate_limit,
            max_workers=args.max_workers
        )

        # 设置日志级别
        if args.verbose:
            logging.getLogger().setLevel(logging.DEBUG)

        print(f"财务数据下载器已初始化")
        print(f"输出目录: {args.output_dir}")
        print(f"API调用间隔: {args.rate_limit} 秒")
        print(f"最大并发数: {args.max_workers}")
        print()

        if args.validate_only:
            # 仅验证数据完整性
            print("验证现有数据完整性...")
            validation_results = downloader.validate_data_integrity(args.tickers)

            print("\n数据完整性验证结果:")
            print("=" * 50)
            for ticker, results in validation_results.items():
                print(f"股票: {ticker}")
                print(f"  总文件数: {results['total_files']}")
                print(f"  有效文件: {results['valid_files']}")
                print(f"  无效文件: {results['invalid_files']}")

                for data_type, type_results in results['data_types'].items():
                    print(f"  {data_type}: {type_results['valid_files']}/{type_results['total_files']} 文件")
                print()

        else:
            # 下载数据
            if args.force_redownload:
                print("警告: 启用强制重新下载模式，将覆盖现有文件")
                # 这里可以添加删除现有文件的逻辑

            print(f"开始下载数据...")
            print(f"股票代码: {', '.join(args.tickers)}")
            print(f"日期范围: {args.start_date} 到 {args.end_date}")
            print(f"数据类型: {', '.join(args.data_types)}")
            print()

            # 执行下载
            results = downloader.download_all_data(
                tickers=args.tickers,
                start_date=args.start_date,
                end_date=args.end_date,
                data_types=args.data_types
            )

            # 生成报告
            report = downloader.generate_report(results)
            print("\n" + report)

            # 验证下载的数据
            print("\n验证下载的数据...")
            validation_results = downloader.validate_data_integrity(args.tickers)

            print("\n最终数据验证结果:")
            print("=" * 50)
            for ticker, results in validation_results.items():
                success_rate = (results['valid_files'] / results['total_files'] * 100) if results['total_files'] > 0 else 0
                print(f"股票: {ticker} - 成功率: {success_rate:.1f}% ({results['valid_files']}/{results['total_files']})")

        print("\n下载完成!")

    except KeyboardInterrupt:
        print("\n下载被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n下载过程中发生错误: {e}")
        logging.exception("详细错误信息:")
        sys.exit(1)


if __name__ == "__main__":
    main()
