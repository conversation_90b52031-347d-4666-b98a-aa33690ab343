2025-07-07 16:55:40,804 - INFO - 开始下载数据: 3 个股票, 6 种数据类型
2025-07-07 16:55:40,806 - INFO - 时间范围: 2025-01-01 到 2025-06-01
2025-07-07 16:55:40,806 - INFO - 数据类型: prices, financial_metrics, insider_trades, company_news, market_cap, line_items
2025-07-07 16:55:40,807 - INFO - 下载股价数据: AAPL (2025-01-01 to 2025-06-01)
2025-07-07 16:55:40,807 - INFO - 下载股价数据: MSFT (2025-01-01 to 2025-06-01)
2025-07-07 16:55:40,808 - INFO - 下载股价数据: NVDA (2025-01-01 to 2025-06-01)
2025-07-07 16:55:41,600 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:42,878 - INFO - 下载财务指标数据: AAPL (2025-01-01)
2025-07-07 16:55:42,880 - INFO - 下载财务指标数据: MSFT (2025-01-01)
2025-07-07 16:55:43,615 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:43,689 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:45,640 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:47,688 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:47,801 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:53,709 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:55,786 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:56,042 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:09,761 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:56:11,892 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:12,276 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:41,842 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:56:41,985 - ERROR - 下载股价数据失败 NVDA: HTTPSConnectionPool(host='api.financialdatasets.ai', port=443): Max retries exceeded with url: /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
2025-07-07 16:56:41,988 - INFO - 下载财务指标数据: NVDA (2025-01-01)
2025-07-07 16:56:43,829 - INFO - 下载内幕交易数据: NVDA (2025-01-01)
2025-07-07 16:56:44,555 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:44,580 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:44,872 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:45,719 - INFO - 下载内幕交易数据: AAPL (2025-01-01)
2025-07-07 16:56:46,028 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:46,389 - INFO - 下载公司新闻数据: NVDA (2025-01-01)
2025-07-07 16:56:47,113 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:56:47,540 - INFO - 下载公司新闻数据: AAPL (2025-01-01)
2025-07-07 16:56:49,630 - INFO - 下载市值数据: AAPL (2025-01-01)
2025-07-07 16:56:50,129 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:50,134 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 16:56:51,248 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:56:51,296 - INFO - 下载内幕交易数据: MSFT (2025-01-01)
2025-07-07 16:56:51,672 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:56:51,672 - INFO - 下载财务指标数据: AAPL (2025-01-08)
2025-07-07 16:56:51,978 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:52,176 - INFO - 下载内幕交易数据: AAPL (2025-01-08)
2025-07-07 16:56:52,679 - INFO - 下载公司新闻数据: AAPL (2025-01-08)
2025-07-07 16:56:53,182 - INFO - 下载市值数据: AAPL (2025-01-08)
2025-07-07 16:56:53,684 - INFO - 下载财务科目数据: AAPL (2025-01-08)
2025-07-07 16:56:55,270 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:56:55,271 - INFO - 下载财务指标数据: AAPL (2025-01-15)
2025-07-07 16:56:55,774 - INFO - 下载内幕交易数据: AAPL (2025-01-15)
2025-07-07 16:56:56,029 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:56,279 - INFO - 下载公司新闻数据: AAPL (2025-01-15)
2025-07-07 16:56:56,782 - INFO - 下载市值数据: AAPL (2025-01-15)
2025-07-07 16:56:57,284 - INFO - 下载财务科目数据: AAPL (2025-01-15)
2025-07-07 16:56:57,931 - INFO - 下载公司新闻数据: MSFT (2025-01-01)
2025-07-07 16:56:57,939 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:56:58,665 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=MSFT&end_date=2025-01-01&limit=100
2025-07-07 16:56:59,542 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:58:43,969 - INFO - 开始下载数据: 3 个股票, 6 种数据类型
2025-07-07 16:58:43,969 - INFO - 时间范围: 2025-01-01 到 2025-06-01
2025-07-07 16:58:43,969 - INFO - 数据类型: prices, financial_metrics, insider_trades, company_news, market_cap, line_items
2025-07-07 16:58:43,970 - INFO - 股价数据已存在: AAPL (2025-01-01 to 2025-06-01)
2025-07-07 16:58:43,971 - INFO - 股价数据已存在: MSFT (2025-01-01 to 2025-06-01)
2025-07-07 16:58:43,971 - INFO - 财务指标数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,971 - INFO - 下载股价数据: NVDA (2025-01-01 to 2025-06-01)
2025-07-07 16:58:43,971 - INFO - 财务指标数据已存在: MSFT (2025-01-01)
2025-07-07 16:58:43,971 - INFO - 内幕交易数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 内幕交易数据已存在: MSFT (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 公司新闻数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 下载公司新闻数据: MSFT (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 市值数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 16:58:44,690 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:44,703 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:44,710 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:45,609 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:45,610 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:45,611 - INFO - 财务指标数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,612 - INFO - 内幕交易数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,612 - INFO - 公司新闻数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,612 - INFO - 市值数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,613 - INFO - 下载财务科目数据: AAPL (2025-01-08)
2025-07-07 16:58:46,004 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=MSFT&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 16:58:46,134 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 HTTP/1.1" 200 None
2025-07-07 16:58:46,251 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:46,279 - DEBUG - 数据已保存到: financial_data_offline\NVDA_prices\NVDA_prices_2025-01-01_to_2025-06-01.json
2025-07-07 16:58:46,280 - INFO - 财务指标数据已存在: NVDA (2025-01-01)
2025-07-07 16:58:46,280 - INFO - 内幕交易数据已存在: NVDA (2025-01-01)
2025-07-07 16:58:46,280 - INFO - 下载公司新闻数据: NVDA (2025-01-01)
2025-07-07 16:58:46,446 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-01.json
2025-07-07 16:58:46,446 - INFO - 下载市值数据: MSFT (2025-01-01)
2025-07-07 16:58:46,920 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:47,078 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:47,163 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:47,164 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:47,165 - INFO - 财务指标数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,166 - INFO - 内幕交易数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,166 - INFO - 公司新闻数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,167 - INFO - 市值数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,167 - INFO - 下载财务科目数据: AAPL (2025-01-15)
2025-07-07 16:58:47,222 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:47,223 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:58:47,224 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:58:47,398 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:47,801 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:47,883 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:47,883 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:58:47,885 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:58:47,952 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=NVDA&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 16:58:47,965 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:48,399 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-01.json
2025-07-07 16:58:48,400 - INFO - 下载市值数据: NVDA (2025-01-01)
2025-07-07 16:58:49,039 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:49,931 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=NVDA&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 16:58:50,159 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-01.json
2025-07-07 16:58:50,159 - INFO - 下载财务科目数据: NVDA (2025-01-01)
2025-07-07 16:58:50,795 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:51,398 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:58:51,398 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:58:51,966 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:58:51,966 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:58:52,116 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:52,117 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:52,118 - INFO - 下载财务指标数据: NVDA (2025-01-08)
2025-07-07 16:58:52,231 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:52,353 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 16:58:52,604 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-01.json
2025-07-07 16:58:52,605 - INFO - 下载财务科目数据: MSFT (2025-01-01)
2025-07-07 16:58:52,621 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-08.json
2025-07-07 16:58:52,622 - INFO - 下载内幕交易数据: NVDA (2025-01-08)
2025-07-07 16:58:53,298 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:53,310 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:54,208 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:54,210 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:54,211 - INFO - 下载财务指标数据: MSFT (2025-01-08)
2025-07-07 16:58:54,714 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-08.json
2025-07-07 16:58:54,715 - INFO - 下载内幕交易数据: MSFT (2025-01-08)
2025-07-07 16:58:54,952 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-08&limit=50 HTTP/1.1" 200 None
2025-07-07 16:58:55,168 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-08.json
2025-07-07 16:58:55,169 - INFO - 下载公司新闻数据: NVDA (2025-01-08)
2025-07-07 16:58:55,354 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:55,673 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-08.json
2025-07-07 16:58:55,674 - INFO - 下载市值数据: NVDA (2025-01-08)
2025-07-07 16:58:56,176 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-08.json
2025-07-07 16:58:56,178 - INFO - 下载财务科目数据: NVDA (2025-01-08)
2025-07-07 16:58:56,976 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-08&limit=50 HTTP/1.1" 200 None
2025-07-07 16:58:57,003 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:57,200 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-08.json
2025-07-07 16:58:57,201 - INFO - 下载公司新闻数据: MSFT (2025-01-08)
2025-07-07 16:58:57,705 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-08.json
2025-07-07 16:58:57,707 - INFO - 下载市值数据: MSFT (2025-01-08)
2025-07-07 16:58:57,915 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:57,915 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:57,916 - INFO - 下载财务指标数据: NVDA (2025-01-15)
2025-07-07 16:58:58,208 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-08.json
2025-07-07 16:58:58,208 - INFO - 下载财务科目数据: MSFT (2025-01-08)
2025-07-07 16:58:58,417 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-15.json
2025-07-07 16:58:58,418 - INFO - 下载内幕交易数据: NVDA (2025-01-15)
2025-07-07 16:58:58,860 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:58,920 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-15.json
2025-07-07 16:58:58,921 - INFO - 下载公司新闻数据: NVDA (2025-01-15)
2025-07-07 16:58:59,425 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-15.json
2025-07-07 16:58:59,425 - INFO - 下载市值数据: NVDA (2025-01-15)
2025-07-07 16:58:59,845 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:59,846 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:59,847 - INFO - 下载财务指标数据: MSFT (2025-01-15)
2025-07-07 16:58:59,927 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-15.json
2025-07-07 16:58:59,928 - INFO - 下载财务科目数据: NVDA (2025-01-15)
2025-07-07 16:59:00,232 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:00,233 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:00,348 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-15.json
2025-07-07 16:59:00,348 - INFO - 下载内幕交易数据: MSFT (2025-01-15)
2025-07-07 16:59:00,454 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:00,612 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:00,851 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-15.json
2025-07-07 16:59:00,852 - INFO - 下载公司新闻数据: MSFT (2025-01-15)
2025-07-07 16:59:01,359 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-15.json
2025-07-07 16:59:01,360 - INFO - 下载市值数据: MSFT (2025-01-15)
2025-07-07 16:59:01,861 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-15.json
2025-07-07 16:59:01,862 - INFO - 下载财务科目数据: MSFT (2025-01-15)
2025-07-07 16:59:02,501 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:02,623 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:02,624 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:02,624 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:03,175 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:03,176 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:03,177 - INFO - 下载财务指标数据: NVDA (2025-01-22)
2025-07-07 16:59:03,680 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-22.json
2025-07-07 16:59:03,681 - INFO - 下载内幕交易数据: NVDA (2025-01-22)
2025-07-07 16:59:04,184 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-22.json
2025-07-07 16:59:04,185 - INFO - 下载公司新闻数据: NVDA (2025-01-22)
2025-07-07 16:59:04,265 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:04,265 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:04,266 - INFO - 下载财务指标数据: MSFT (2025-01-22)
2025-07-07 16:59:04,692 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-22.json
2025-07-07 16:59:04,693 - INFO - 下载市值数据: NVDA (2025-01-22)
2025-07-07 16:59:04,769 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-22.json
2025-07-07 16:59:04,770 - INFO - 下载内幕交易数据: MSFT (2025-01-22)
2025-07-07 16:59:05,195 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-22.json
2025-07-07 16:59:05,195 - INFO - 下载财务科目数据: NVDA (2025-01-22)
2025-07-07 16:59:05,275 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-22.json
2025-07-07 16:59:05,276 - INFO - 下载公司新闻数据: MSFT (2025-01-22)
2025-07-07 16:59:05,779 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-22.json
2025-07-07 16:59:05,779 - INFO - 下载市值数据: MSFT (2025-01-22)
2025-07-07 16:59:05,835 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:06,136 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:06,136 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:06,136 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:06,204 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:06,281 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-22.json
2025-07-07 16:59:06,281 - INFO - 下载财务科目数据: MSFT (2025-01-22)
2025-07-07 16:59:06,934 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:07,001 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:07,001 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:07,002 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:07,084 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:10,205 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:10,206 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:59:10,332 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:11,086 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:11,086 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:59:11,142 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:16,455 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:16,456 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 16:59:16,538 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:18,332 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:18,332 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:19,144 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:19,144 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:19,207 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:19,261 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:19,261 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:19,261 - INFO - 下载财务指标数据: NVDA (2025-01-29)
2025-07-07 16:59:19,766 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-29.json
2025-07-07 16:59:19,766 - INFO - 下载内幕交易数据: NVDA (2025-01-29)
2025-07-07 16:59:20,271 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-29.json
2025-07-07 16:59:20,272 - INFO - 下载公司新闻数据: NVDA (2025-01-29)
2025-07-07 16:59:20,777 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-29.json
2025-07-07 16:59:20,778 - INFO - 下载市值数据: NVDA (2025-01-29)
2025-07-07 16:59:21,281 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-29.json
2025-07-07 16:59:21,282 - INFO - 下载财务科目数据: NVDA (2025-01-29)
2025-07-07 16:59:21,927 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:22,811 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:22,812 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:22,812 - INFO - 下载财务指标数据: NVDA (2025-02-05)
2025-07-07 16:59:23,316 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-05.json
2025-07-07 16:59:23,316 - INFO - 下载内幕交易数据: NVDA (2025-02-05)
2025-07-07 16:59:23,820 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-05.json
2025-07-07 16:59:23,820 - INFO - 下载公司新闻数据: NVDA (2025-02-05)
2025-07-07 16:59:24,326 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-05.json
2025-07-07 16:59:24,326 - INFO - 下载市值数据: NVDA (2025-02-05)
2025-07-07 16:59:24,828 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-05.json
2025-07-07 16:59:24,829 - INFO - 下载财务科目数据: NVDA (2025-02-05)
2025-07-07 16:59:25,460 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:26,387 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:26,388 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:26,391 - INFO - 下载财务指标数据: NVDA (2025-02-12)
2025-07-07 16:59:26,893 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-12.json
2025-07-07 16:59:26,894 - INFO - 下载内幕交易数据: NVDA (2025-02-12)
2025-07-07 16:59:27,396 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-12.json
2025-07-07 16:59:27,396 - INFO - 下载公司新闻数据: NVDA (2025-02-12)
2025-07-07 16:59:27,902 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-12.json
2025-07-07 16:59:27,902 - INFO - 下载市值数据: NVDA (2025-02-12)
2025-07-07 16:59:28,405 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-12.json
2025-07-07 16:59:28,406 - INFO - 下载财务科目数据: NVDA (2025-02-12)
2025-07-07 16:59:29,041 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:29,131 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:29,131 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:29,132 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:29,152 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:33,153 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:33,154 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:59:33,176 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:35,208 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:35,209 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 16:59:35,234 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:41,177 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:41,177 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:41,266 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:48,539 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:48,540 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 16:59:49,442 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:49,443 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:49,444 - INFO - 下载财务指标数据: AAPL (2025-01-22)
2025-07-07 16:59:50,077 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:50,163 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-22&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:50,163 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-22&limit=10&period=ttm
2025-07-07 16:59:50,163 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:51,126 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-22&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 16:59:51,340 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-22.json
2025-07-07 16:59:51,341 - INFO - 下载内幕交易数据: AAPL (2025-01-22)
2025-07-07 16:59:52,012 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:52,501 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=AAPL&filing_date_lte=2025-01-22&limit=50'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:52,501 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-22&limit=50
2025-07-07 16:59:52,501 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:54,037 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-22&limit=50 HTTP/1.1" 200 None
2025-07-07 16:59:54,331 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-22.json
2025-07-07 16:59:54,332 - INFO - 下载公司新闻数据: AAPL (2025-01-22)
2025-07-07 16:59:54,968 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:56,005 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=AAPL&end_date=2025-01-22&limit=100 HTTP/1.1" 200 None
2025-07-07 16:59:56,435 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-22.json
2025-07-07 16:59:56,435 - INFO - 下载市值数据: AAPL (2025-01-22)
2025-07-07 16:59:56,936 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-22.json
2025-07-07 16:59:56,937 - INFO - 下载财务科目数据: AAPL (2025-01-22)
2025-07-07 16:59:57,267 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:57,267 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 16:59:57,375 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:57,572 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:57,660 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:57,660 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:57,661 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:58,470 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:02,471 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:02,471 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:03,712 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:03,713 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:03,713 - INFO - 下载财务指标数据: AAPL (2025-01-29)
2025-07-07 17:00:04,216 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-29.json
2025-07-07 17:00:04,216 - INFO - 下载内幕交易数据: AAPL (2025-01-29)
2025-07-07 17:00:04,720 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-29.json
2025-07-07 17:00:04,722 - INFO - 下载公司新闻数据: AAPL (2025-01-29)
2025-07-07 17:00:05,227 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-29.json
2025-07-07 17:00:05,228 - INFO - 下载市值数据: AAPL (2025-01-29)
2025-07-07 17:00:05,732 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-29.json
2025-07-07 17:00:05,732 - INFO - 下载财务科目数据: AAPL (2025-01-29)
2025-07-07 17:00:06,368 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:07,235 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:07,235 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:00:07,300 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:07,301 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:07,302 - INFO - 下载财务指标数据: AAPL (2025-02-05)
2025-07-07 17:00:07,803 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-05.json
2025-07-07 17:00:07,803 - INFO - 下载内幕交易数据: AAPL (2025-02-05)
2025-07-07 17:00:08,307 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-05.json
2025-07-07 17:00:08,308 - INFO - 下载公司新闻数据: AAPL (2025-02-05)
2025-07-07 17:00:08,318 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:00:08,811 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-05.json
2025-07-07 17:00:08,812 - INFO - 下载市值数据: AAPL (2025-02-05)
2025-07-07 17:00:09,258 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:09,259 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:09,260 - INFO - 下载财务指标数据: MSFT (2025-01-29)
2025-07-07 17:00:09,312 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-05.json
2025-07-07 17:00:09,312 - INFO - 下载财务科目数据: AAPL (2025-02-05)
2025-07-07 17:00:09,764 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-29.json
2025-07-07 17:00:09,764 - INFO - 下载内幕交易数据: MSFT (2025-01-29)
2025-07-07 17:00:09,946 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:10,094 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:10,094 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:10,096 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:10,167 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:10,269 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-29.json
2025-07-07 17:00:10,270 - INFO - 下载公司新闻数据: MSFT (2025-01-29)
2025-07-07 17:00:10,776 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-29.json
2025-07-07 17:00:10,777 - INFO - 下载市值数据: MSFT (2025-01-29)
2025-07-07 17:00:11,279 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-29.json
2025-07-07 17:00:11,280 - INFO - 下载财务科目数据: MSFT (2025-01-29)
2025-07-07 17:00:11,920 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:12,235 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:12,236 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:12,236 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:12,298 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:14,168 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:14,168 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:15,170 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:15,170 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:15,170 - INFO - 下载财务指标数据: AAPL (2025-02-12)
2025-07-07 17:00:15,674 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-12.json
2025-07-07 17:00:15,675 - INFO - 下载内幕交易数据: AAPL (2025-02-12)
2025-07-07 17:00:16,176 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-12.json
2025-07-07 17:00:16,177 - INFO - 下载公司新闻数据: AAPL (2025-02-12)
2025-07-07 17:00:16,299 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:16,299 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:16,683 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-12.json
2025-07-07 17:00:16,684 - INFO - 下载市值数据: AAPL (2025-02-12)
2025-07-07 17:00:17,185 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-12.json
2025-07-07 17:00:17,186 - INFO - 下载财务科目数据: AAPL (2025-02-12)
2025-07-07 17:00:17,325 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:17,325 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:17,326 - INFO - 下载财务指标数据: MSFT (2025-02-05)
2025-07-07 17:00:17,828 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-05.json
2025-07-07 17:00:17,831 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:17,831 - INFO - 下载内幕交易数据: MSFT (2025-02-05)
2025-07-07 17:00:18,333 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-05.json
2025-07-07 17:00:18,334 - INFO - 下载公司新闻数据: MSFT (2025-02-05)
2025-07-07 17:00:18,837 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-05.json
2025-07-07 17:00:18,838 - INFO - 下载市值数据: MSFT (2025-02-05)
2025-07-07 17:00:19,195 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:19,195 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:19,196 - INFO - 下载财务指标数据: AAPL (2025-02-19)
2025-07-07 17:00:19,340 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-05.json
2025-07-07 17:00:19,341 - INFO - 下载财务科目数据: MSFT (2025-02-05)
2025-07-07 17:00:19,697 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-19.json
2025-07-07 17:00:19,698 - INFO - 下载内幕交易数据: AAPL (2025-02-19)
2025-07-07 17:00:19,996 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:20,030 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:20,030 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:20,030 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:20,061 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:20,200 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-19.json
2025-07-07 17:00:20,200 - INFO - 下载公司新闻数据: AAPL (2025-02-19)
2025-07-07 17:00:20,707 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-19.json
2025-07-07 17:00:20,708 - INFO - 下载市值数据: AAPL (2025-02-19)
2025-07-07 17:00:21,209 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-19.json
2025-07-07 17:00:21,209 - INFO - 下载财务科目数据: AAPL (2025-02-19)
2025-07-07 17:00:21,845 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:21,924 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:21,925 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:21,926 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:22,241 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:24,062 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:24,063 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:24,185 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:26,242 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:26,242 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:26,315 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:29,376 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:29,376 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:00:30,254 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:30,255 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:30,257 - INFO - 下载财务指标数据: NVDA (2025-02-19)
2025-07-07 17:00:30,759 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-19.json
2025-07-07 17:00:30,760 - INFO - 下载内幕交易数据: NVDA (2025-02-19)
2025-07-07 17:00:31,262 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-19.json
2025-07-07 17:00:31,262 - INFO - 下载公司新闻数据: NVDA (2025-02-19)
2025-07-07 17:00:31,766 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-19.json
2025-07-07 17:00:31,767 - INFO - 下载市值数据: NVDA (2025-02-19)
2025-07-07 17:00:32,186 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:32,186 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:32,269 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-19.json
2025-07-07 17:00:32,269 - INFO - 下载财务科目数据: NVDA (2025-02-19)
2025-07-07 17:00:32,904 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:33,100 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:33,100 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:33,101 - INFO - 下载财务指标数据: MSFT (2025-02-12)
2025-07-07 17:00:33,603 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-12.json
2025-07-07 17:00:33,603 - INFO - 下载内幕交易数据: MSFT (2025-02-12)
2025-07-07 17:00:33,855 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:33,855 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:33,857 - INFO - 下载财务指标数据: NVDA (2025-02-26)
2025-07-07 17:00:34,107 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-12.json
2025-07-07 17:00:34,108 - INFO - 下载公司新闻数据: MSFT (2025-02-12)
2025-07-07 17:00:34,316 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:34,316 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:34,359 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-26.json
2025-07-07 17:00:34,359 - INFO - 下载内幕交易数据: NVDA (2025-02-26)
2025-07-07 17:00:34,374 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:34,612 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-12.json
2025-07-07 17:00:34,614 - INFO - 下载市值数据: MSFT (2025-02-12)
2025-07-07 17:00:34,861 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-26.json
2025-07-07 17:00:34,861 - INFO - 下载公司新闻数据: NVDA (2025-02-26)
2025-07-07 17:00:35,115 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-12.json
2025-07-07 17:00:35,115 - INFO - 下载财务科目数据: MSFT (2025-02-12)
2025-07-07 17:00:35,366 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-26.json
2025-07-07 17:00:35,367 - INFO - 下载市值数据: NVDA (2025-02-26)
2025-07-07 17:00:35,750 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:35,811 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:35,811 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:35,811 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:35,862 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:35,869 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-26.json
2025-07-07 17:00:35,869 - INFO - 下载财务科目数据: NVDA (2025-02-26)
2025-07-07 17:00:36,501 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:36,545 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:36,545 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:36,545 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:36,584 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:39,862 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:39,863 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:39,906 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:40,585 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:40,585 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:40,622 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:47,908 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:47,909 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:47,949 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:48,624 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:48,624 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:49,596 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:49,597 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:49,597 - INFO - 下载财务指标数据: NVDA (2025-03-05)
2025-07-07 17:00:50,099 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-05.json
2025-07-07 17:00:50,099 - INFO - 下载内幕交易数据: NVDA (2025-03-05)
2025-07-07 17:00:50,375 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:50,375 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:00:50,604 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-05.json
2025-07-07 17:00:50,604 - INFO - 下载公司新闻数据: NVDA (2025-03-05)
2025-07-07 17:00:51,108 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-05.json
2025-07-07 17:00:51,109 - INFO - 下载市值数据: NVDA (2025-03-05)
2025-07-07 17:00:51,240 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:51,240 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:51,241 - INFO - 下载财务指标数据: AAPL (2025-02-26)
2025-07-07 17:00:51,611 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-05.json
2025-07-07 17:00:51,611 - INFO - 下载财务科目数据: NVDA (2025-03-05)
2025-07-07 17:00:51,742 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-26.json
2025-07-07 17:00:51,742 - INFO - 下载内幕交易数据: AAPL (2025-02-26)
2025-07-07 17:00:52,241 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:52,244 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-26.json
2025-07-07 17:00:52,244 - INFO - 下载公司新闻数据: AAPL (2025-02-26)
2025-07-07 17:00:52,287 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:52,287 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:52,287 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:52,327 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:52,746 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-26.json
2025-07-07 17:00:52,746 - INFO - 下载市值数据: AAPL (2025-02-26)
2025-07-07 17:00:53,248 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-26.json
2025-07-07 17:00:53,249 - INFO - 下载财务科目数据: AAPL (2025-02-26)
2025-07-07 17:00:53,876 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:53,916 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:53,917 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:53,917 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:53,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:56,328 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:56,328 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:56,379 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:57,967 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:57,968 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:58,010 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:03,950 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:03,951 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:01:04,011 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:04,380 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:04,380 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:04,425 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:06,011 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:06,011 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:06,094 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:20,427 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:20,427 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:01:20,463 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:22,095 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:22,095 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:01:24,149 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:24,150 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:24,151 - INFO - 下载财务指标数据: AAPL (2025-03-05)
2025-07-07 17:01:24,654 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-05.json
2025-07-07 17:01:24,655 - INFO - 下载内幕交易数据: AAPL (2025-03-05)
2025-07-07 17:01:25,158 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-05.json
2025-07-07 17:01:25,159 - INFO - 下载公司新闻数据: AAPL (2025-03-05)
2025-07-07 17:01:25,661 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-05.json
2025-07-07 17:01:25,662 - INFO - 下载市值数据: AAPL (2025-03-05)
2025-07-07 17:01:26,164 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-05.json
2025-07-07 17:01:26,165 - INFO - 下载财务科目数据: AAPL (2025-03-05)
2025-07-07 17:01:26,800 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:26,888 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:26,888 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:26,889 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:27,002 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:31,002 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:31,004 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:01:31,101 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:36,012 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:36,013 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:01:37,122 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:01:37,208 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:37,209 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:37,210 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:01:38,138 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:38,139 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:38,140 - INFO - 下载财务指标数据: MSFT (2025-02-19)
2025-07-07 17:01:38,641 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-19.json
2025-07-07 17:01:38,642 - INFO - 下载内幕交易数据: MSFT (2025-02-19)
2025-07-07 17:01:39,103 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:39,103 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:39,143 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-19.json
2025-07-07 17:01:39,144 - INFO - 下载公司新闻数据: MSFT (2025-02-19)
2025-07-07 17:01:39,648 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-19.json
2025-07-07 17:01:39,649 - INFO - 下载市值数据: MSFT (2025-02-19)
2025-07-07 17:01:40,028 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:40,029 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:40,030 - INFO - 下载财务指标数据: AAPL (2025-03-12)
2025-07-07 17:01:40,151 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-19.json
2025-07-07 17:01:40,152 - INFO - 下载财务科目数据: MSFT (2025-02-19)
2025-07-07 17:01:40,532 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-12.json
2025-07-07 17:01:40,533 - INFO - 下载内幕交易数据: AAPL (2025-03-12)
2025-07-07 17:01:40,790 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:40,845 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:40,845 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:40,845 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:40,900 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:41,036 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-12.json
2025-07-07 17:01:41,037 - INFO - 下载公司新闻数据: AAPL (2025-03-12)
2025-07-07 17:01:41,542 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-12.json
2025-07-07 17:01:41,542 - INFO - 下载市值数据: AAPL (2025-03-12)
2025-07-07 17:01:42,045 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-12.json
2025-07-07 17:01:42,046 - INFO - 下载财务科目数据: AAPL (2025-03-12)
2025-07-07 17:01:42,684 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:42,796 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:42,796 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:42,796 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:42,919 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:44,901 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:44,901 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:01:44,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:46,920 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:46,920 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:01:47,004 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:52,464 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:52,465 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:01:52,967 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:52,968 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:53,069 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:53,453 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:53,454 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:53,454 - INFO - 下载财务指标数据: NVDA (2025-03-12)
2025-07-07 17:01:53,956 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-12.json
2025-07-07 17:01:53,956 - INFO - 下载内幕交易数据: NVDA (2025-03-12)
2025-07-07 17:01:54,458 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-12.json
2025-07-07 17:01:54,459 - INFO - 下载公司新闻数据: NVDA (2025-03-12)
2025-07-07 17:01:54,961 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-12.json
2025-07-07 17:01:54,962 - INFO - 下载市值数据: NVDA (2025-03-12)
2025-07-07 17:01:55,006 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:55,007 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:55,108 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:55,463 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-12.json
2025-07-07 17:01:55,463 - INFO - 下载财务科目数据: NVDA (2025-03-12)
2025-07-07 17:01:56,097 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:57,004 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:57,006 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:57,007 - INFO - 下载财务指标数据: NVDA (2025-03-19)
2025-07-07 17:01:57,509 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-19.json
2025-07-07 17:01:57,509 - INFO - 下载内幕交易数据: NVDA (2025-03-19)
2025-07-07 17:01:58,012 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-19.json
2025-07-07 17:01:58,012 - INFO - 下载公司新闻数据: NVDA (2025-03-19)
2025-07-07 17:01:58,515 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-19.json
2025-07-07 17:01:58,516 - INFO - 下载市值数据: NVDA (2025-03-19)
2025-07-07 17:01:59,017 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-19.json
2025-07-07 17:01:59,018 - INFO - 下载财务科目数据: NVDA (2025-03-19)
2025-07-07 17:01:59,652 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:59,729 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:59,729 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:59,730 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:59,775 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:03,777 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:03,777 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:02:03,819 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:09,070 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:09,070 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:10,024 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:02:10,025 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:02:10,026 - INFO - 下载财务指标数据: MSFT (2025-02-26)
2025-07-07 17:02:10,528 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-26.json
2025-07-07 17:02:10,529 - INFO - 下载内幕交易数据: MSFT (2025-02-26)
2025-07-07 17:02:11,032 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-26.json
2025-07-07 17:02:11,033 - INFO - 下载公司新闻数据: MSFT (2025-02-26)
2025-07-07 17:02:11,110 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:11,111 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:11,535 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-26.json
2025-07-07 17:02:11,535 - INFO - 下载市值数据: MSFT (2025-02-26)
2025-07-07 17:02:11,819 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:11,819 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:02:11,932 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:12,038 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-26.json
2025-07-07 17:02:12,039 - INFO - 下载财务科目数据: MSFT (2025-02-26)
2025-07-07 17:02:12,215 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:12,679 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:02:13,633 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:02:13,633 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:02:13,634 - INFO - 下载财务指标数据: MSFT (2025-03-05)
2025-07-07 17:02:14,136 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-05.json
2025-07-07 17:02:14,137 - INFO - 下载内幕交易数据: MSFT (2025-03-05)
2025-07-07 17:02:14,641 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-05.json
2025-07-07 17:02:14,643 - INFO - 下载公司新闻数据: MSFT (2025-03-05)
2025-07-07 17:02:15,147 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-05.json
2025-07-07 17:02:15,148 - INFO - 下载市值数据: MSFT (2025-03-05)
2025-07-07 17:02:15,650 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-05.json
2025-07-07 17:02:15,650 - INFO - 下载财务科目数据: MSFT (2025-03-05)
2025-07-07 17:02:16,287 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:02:16,378 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:16,378 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:16,379 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:02:16,458 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:20,460 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:20,460 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:02:21,584 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:27,934 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:27,934 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:27,997 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:29,584 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:29,585 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:02:29,624 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:44,216 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:44,217 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:02:45,377 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:02:45,465 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:45,465 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:45,465 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:02:45,557 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:45,626 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:45,626 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:45,710 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:49,558 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:49,558 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:02:49,596 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:57,597 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:57,597 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:02:57,774 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:59,998 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:59,998 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:03:00,971 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:00,972 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:00,974 - INFO - 下载财务指标数据: NVDA (2025-03-26)
2025-07-07 17:03:01,475 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-26.json
2025-07-07 17:03:01,476 - INFO - 下载内幕交易数据: NVDA (2025-03-26)
2025-07-07 17:03:01,978 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-26.json
2025-07-07 17:03:01,979 - INFO - 下载公司新闻数据: NVDA (2025-03-26)
2025-07-07 17:03:02,483 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-26.json
2025-07-07 17:03:02,483 - INFO - 下载市值数据: NVDA (2025-03-26)
2025-07-07 17:03:02,986 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-26.json
2025-07-07 17:03:02,986 - INFO - 下载财务科目数据: NVDA (2025-03-26)
2025-07-07 17:03:03,623 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:04,501 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:04,502 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:04,503 - INFO - 下载财务指标数据: NVDA (2025-04-02)
2025-07-07 17:03:05,006 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-02.json
2025-07-07 17:03:05,007 - INFO - 下载内幕交易数据: NVDA (2025-04-02)
2025-07-07 17:03:05,510 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-02.json
2025-07-07 17:03:05,511 - INFO - 下载公司新闻数据: NVDA (2025-04-02)
2025-07-07 17:03:06,015 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-02.json
2025-07-07 17:03:06,016 - INFO - 下载市值数据: NVDA (2025-04-02)
2025-07-07 17:03:06,517 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-02.json
2025-07-07 17:03:06,517 - INFO - 下载财务科目数据: NVDA (2025-04-02)
2025-07-07 17:03:07,161 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:07,253 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:07,253 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:07,254 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:07,325 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:11,327 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:11,327 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:11,368 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:13,775 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:13,776 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:03:14,720 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:14,720 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:14,721 - INFO - 下载财务指标数据: AAPL (2025-03-19)
2025-07-07 17:03:15,224 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-19.json
2025-07-07 17:03:15,225 - INFO - 下载内幕交易数据: AAPL (2025-03-19)
2025-07-07 17:03:15,728 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-19.json
2025-07-07 17:03:15,729 - INFO - 下载公司新闻数据: AAPL (2025-03-19)
2025-07-07 17:03:16,231 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-19.json
2025-07-07 17:03:16,231 - INFO - 下载市值数据: AAPL (2025-03-19)
2025-07-07 17:03:16,732 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-19.json
2025-07-07 17:03:16,732 - INFO - 下载财务科目数据: AAPL (2025-03-19)
2025-07-07 17:03:17,375 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:17,476 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:17,476 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:17,476 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:17,711 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:17,711 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:03:18,440 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:18,441 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:18,442 - INFO - 下载财务指标数据: AAPL (2025-03-26)
2025-07-07 17:03:18,778 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:03:18,871 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:18,871 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:18,872 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:03:18,943 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-26.json
2025-07-07 17:03:18,944 - INFO - 下载内幕交易数据: AAPL (2025-03-26)
2025-07-07 17:03:18,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:19,369 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:19,371 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:19,447 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-26.json
2025-07-07 17:03:19,447 - INFO - 下载公司新闻数据: AAPL (2025-03-26)
2025-07-07 17:03:19,463 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:19,950 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-26.json
2025-07-07 17:03:19,951 - INFO - 下载市值数据: AAPL (2025-03-26)
2025-07-07 17:03:20,454 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-26.json
2025-07-07 17:03:20,454 - INFO - 下载财务科目数据: AAPL (2025-03-26)
2025-07-07 17:03:21,086 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:21,189 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:21,191 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:21,192 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:22,142 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:22,142 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:22,143 - INFO - 下载财务指标数据: AAPL (2025-04-02)
2025-07-07 17:03:22,645 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-02.json
2025-07-07 17:03:22,646 - INFO - 下载内幕交易数据: AAPL (2025-04-02)
2025-07-07 17:03:22,967 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:22,967 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:03:23,027 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:23,147 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-02.json
2025-07-07 17:03:23,147 - INFO - 下载公司新闻数据: AAPL (2025-04-02)
2025-07-07 17:03:23,652 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-02.json
2025-07-07 17:03:23,652 - INFO - 下载市值数据: AAPL (2025-04-02)
2025-07-07 17:03:24,154 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-02.json
2025-07-07 17:03:24,157 - INFO - 下载财务科目数据: AAPL (2025-04-02)
2025-07-07 17:03:24,785 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:24,844 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:24,845 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:24,845 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:24,935 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:28,936 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:28,937 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:29,027 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:31,029 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:31,029 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:03:31,866 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:31,867 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:31,867 - INFO - 下载财务指标数据: MSFT (2025-03-12)
2025-07-07 17:03:32,368 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-12.json
2025-07-07 17:03:32,369 - INFO - 下载内幕交易数据: MSFT (2025-03-12)
2025-07-07 17:03:32,874 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-12.json
2025-07-07 17:03:32,874 - INFO - 下载公司新闻数据: MSFT (2025-03-12)
2025-07-07 17:03:33,378 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-12.json
2025-07-07 17:03:33,379 - INFO - 下载市值数据: MSFT (2025-03-12)
2025-07-07 17:03:33,881 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-12.json
2025-07-07 17:03:33,881 - INFO - 下载财务科目数据: MSFT (2025-03-12)
2025-07-07 17:03:34,516 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:34,566 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:34,566 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:34,566 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:34,640 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:35,464 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:35,465 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:03:35,509 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:37,028 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:37,029 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:37,888 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:37,888 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:37,889 - INFO - 下载财务指标数据: AAPL (2025-04-09)
2025-07-07 17:03:38,391 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-09.json
2025-07-07 17:03:38,392 - INFO - 下载内幕交易数据: AAPL (2025-04-09)
2025-07-07 17:03:38,642 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:38,642 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:38,682 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:38,895 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-09.json
2025-07-07 17:03:38,896 - INFO - 下载公司新闻数据: AAPL (2025-04-09)
2025-07-07 17:03:39,400 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-09.json
2025-07-07 17:03:39,401 - INFO - 下载市值数据: AAPL (2025-04-09)
2025-07-07 17:03:39,902 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-09.json
2025-07-07 17:03:39,902 - INFO - 下载财务科目数据: AAPL (2025-04-09)
2025-07-07 17:03:40,534 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:40,574 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:40,575 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:40,575 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:40,617 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:44,618 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:44,619 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:44,671 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:46,683 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:46,684 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:47,557 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:47,558 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:47,558 - INFO - 下载财务指标数据: MSFT (2025-03-19)
2025-07-07 17:03:48,061 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-19.json
2025-07-07 17:03:48,062 - INFO - 下载内幕交易数据: MSFT (2025-03-19)
2025-07-07 17:03:48,566 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-19.json
2025-07-07 17:03:48,567 - INFO - 下载公司新闻数据: MSFT (2025-03-19)
2025-07-07 17:03:49,071 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-19.json
2025-07-07 17:03:49,072 - INFO - 下载市值数据: MSFT (2025-03-19)
2025-07-07 17:03:49,574 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-19.json
2025-07-07 17:03:49,576 - INFO - 下载财务科目数据: MSFT (2025-03-19)
2025-07-07 17:03:50,212 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:50,253 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:50,253 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:50,253 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:50,295 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:52,672 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:52,673 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:52,710 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:54,296 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:54,296 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:54,335 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:02,336 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:02,336 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:04:02,384 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:07,510 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:07,510 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:04:08,550 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:04:08,710 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:08,710 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:04:08,744 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:09,500 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:04:09,500 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:04:09,500 - INFO - 下载财务指标数据: NVDA (2025-04-09)
2025-07-07 17:04:10,003 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-09.json
2025-07-07 17:04:10,003 - INFO - 下载内幕交易数据: NVDA (2025-04-09)
2025-07-07 17:04:10,507 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-09.json
2025-07-07 17:04:10,508 - INFO - 下载公司新闻数据: NVDA (2025-04-09)
2025-07-07 17:04:11,012 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-09.json
2025-07-07 17:04:11,013 - INFO - 下载市值数据: NVDA (2025-04-09)
2025-07-07 17:04:11,515 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-09.json
2025-07-07 17:04:11,516 - INFO - 下载财务科目数据: NVDA (2025-04-09)
2025-07-07 17:04:12,159 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:04:12,200 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:12,201 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:12,201 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:04:12,245 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:16,246 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:16,247 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:04:16,319 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:18,385 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:18,386 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:04:18,425 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:24,320 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:24,320 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:04:24,354 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:40,355 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:40,356 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:04:40,398 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:40,745 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:40,745 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:04:41,785 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:04:41,871 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:41,872 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:41,873 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:04:41,958 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:45,960 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:45,960 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:04:46,000 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:50,426 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:50,426 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:04:51,478 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:04:51,512 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:51,512 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:51,514 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:04:51,550 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:54,001 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:54,002 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:04:54,039 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:55,550 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:55,551 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:04:55,588 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:03,589 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:03,590 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:05:03,628 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:10,040 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:10,041 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:05:10,081 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:12,399 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:12,399 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:05:13,444 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:05:13,484 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:13,484 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:13,485 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:05:14,374 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:05:14,375 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:05:14,376 - INFO - 下载财务指标数据: NVDA (2025-04-16)
2025-07-07 17:05:14,878 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-16.json
2025-07-07 17:05:14,878 - INFO - 下载内幕交易数据: NVDA (2025-04-16)
2025-07-07 17:05:15,381 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-16.json
2025-07-07 17:05:15,383 - INFO - 下载公司新闻数据: NVDA (2025-04-16)
2025-07-07 17:05:15,891 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-16.json
2025-07-07 17:05:15,892 - INFO - 下载市值数据: NVDA (2025-04-16)
2025-07-07 17:05:16,394 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-16.json
2025-07-07 17:05:16,394 - INFO - 下载财务科目数据: NVDA (2025-04-16)
2025-07-07 17:05:17,032 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:05:17,086 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:17,086 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:17,087 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:05:17,146 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:19,629 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:19,629 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:05:19,669 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:21,146 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:21,146 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:05:22,025 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:05:22,025 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:05:22,026 - INFO - 下载财务指标数据: NVDA (2025-04-23)
2025-07-07 17:05:22,528 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-23.json
2025-07-07 17:05:22,529 - INFO - 下载内幕交易数据: NVDA (2025-04-23)
2025-07-07 17:05:23,031 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-23.json
2025-07-07 17:05:23,031 - INFO - 下载公司新闻数据: NVDA (2025-04-23)
2025-07-07 17:05:23,535 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-23.json
2025-07-07 17:05:23,536 - INFO - 下载市值数据: NVDA (2025-04-23)
2025-07-07 17:05:24,038 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-23.json
2025-07-07 17:05:24,040 - INFO - 下载财务科目数据: NVDA (2025-04-23)
2025-07-07 17:05:24,676 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:05:24,721 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:24,721 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:24,721 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:05:24,767 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:28,768 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:28,768 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:05:28,804 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:36,806 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:36,807 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:05:36,849 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:42,082 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:42,082 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:05:44,120 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:05:44,164 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:44,164 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:44,164 - DEBUG - Starting new HTTPS connection (14): api.financialdatasets.ai:443
2025-07-07 17:05:44,206 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:48,208 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:48,208 - DEBUG - Starting new HTTPS connection (15): api.financialdatasets.ai:443
2025-07-07 17:05:48,251 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:51,669 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:51,670 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:05:52,849 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:52,849 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:05:52,893 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:53,712 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:05:53,750 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:53,751 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:53,751 - DEBUG - Starting new HTTPS connection (14): api.financialdatasets.ai:443
2025-07-07 17:05:53,790 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:56,251 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:56,251 - DEBUG - Starting new HTTPS connection (16): api.financialdatasets.ai:443
2025-07-07 17:05:57,140 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:05:57,140 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:05:57,140 - INFO - 下载财务指标数据: AAPL (2025-04-16)
2025-07-07 17:05:57,643 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-16.json
2025-07-07 17:05:57,644 - INFO - 下载内幕交易数据: AAPL (2025-04-16)
2025-07-07 17:05:57,792 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:57,792 - DEBUG - Starting new HTTPS connection (15): api.financialdatasets.ai:443
2025-07-07 17:05:57,894 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:58,147 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-16.json
2025-07-07 17:05:58,148 - INFO - 下载公司新闻数据: AAPL (2025-04-16)
2025-07-07 17:05:58,650 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-16.json
2025-07-07 17:05:58,651 - INFO - 下载市值数据: AAPL (2025-04-16)
2025-07-07 17:05:59,153 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-16.json
2025-07-07 17:05:59,153 - INFO - 下载财务科目数据: AAPL (2025-04-16)
2025-07-07 17:05:59,789 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:05:59,885 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:59,885 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:59,885 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:00,808 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:00,809 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:00,809 - INFO - 下载财务指标数据: AAPL (2025-04-23)
2025-07-07 17:06:01,311 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-23.json
2025-07-07 17:06:01,312 - INFO - 下载内幕交易数据: AAPL (2025-04-23)
2025-07-07 17:06:01,814 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-23.json
2025-07-07 17:06:01,814 - INFO - 下载公司新闻数据: AAPL (2025-04-23)
2025-07-07 17:06:02,318 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-23.json
2025-07-07 17:06:02,319 - INFO - 下载市值数据: AAPL (2025-04-23)
2025-07-07 17:06:02,822 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-23.json
2025-07-07 17:06:02,823 - INFO - 下载财务科目数据: AAPL (2025-04-23)
2025-07-07 17:06:03,458 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:03,488 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:03,488 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:03,489 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:03,524 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:05,894 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:05,894 - DEBUG - Starting new HTTPS connection (16): api.financialdatasets.ai:443
2025-07-07 17:06:05,930 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:07,525 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:07,525 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:06:07,623 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:15,625 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:15,625 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:06:16,561 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:16,561 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:16,562 - INFO - 下载财务指标数据: AAPL (2025-04-30)
2025-07-07 17:06:17,065 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-30.json
2025-07-07 17:06:17,066 - INFO - 下载内幕交易数据: AAPL (2025-04-30)
2025-07-07 17:06:17,569 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-30.json
2025-07-07 17:06:17,570 - INFO - 下载公司新闻数据: AAPL (2025-04-30)
2025-07-07 17:06:18,075 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-30.json
2025-07-07 17:06:18,076 - INFO - 下载市值数据: AAPL (2025-04-30)
2025-07-07 17:06:18,577 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-30.json
2025-07-07 17:06:18,578 - INFO - 下载财务科目数据: AAPL (2025-04-30)
2025-07-07 17:06:19,209 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:20,111 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:20,112 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:20,113 - INFO - 下载财务指标数据: AAPL (2025-05-07)
2025-07-07 17:06:20,616 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-07.json
2025-07-07 17:06:20,617 - INFO - 下载内幕交易数据: AAPL (2025-05-07)
2025-07-07 17:06:21,119 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-07.json
2025-07-07 17:06:21,119 - INFO - 下载公司新闻数据: AAPL (2025-05-07)
2025-07-07 17:06:21,622 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-07.json
2025-07-07 17:06:21,623 - INFO - 下载市值数据: AAPL (2025-05-07)
2025-07-07 17:06:21,931 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:21,931 - DEBUG - Starting new HTTPS connection (17): api.financialdatasets.ai:443
2025-07-07 17:06:21,972 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:22,125 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-07.json
2025-07-07 17:06:22,125 - INFO - 下载财务科目数据: AAPL (2025-05-07)
2025-07-07 17:06:22,763 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:22,794 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:22,794 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:22,794 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:22,832 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:24,894 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:24,895 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:06:25,750 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:25,750 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:25,751 - INFO - 下载财务指标数据: NVDA (2025-04-30)
2025-07-07 17:06:26,253 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-30.json
2025-07-07 17:06:26,253 - INFO - 下载内幕交易数据: NVDA (2025-04-30)
2025-07-07 17:06:26,757 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-30.json
2025-07-07 17:06:26,758 - INFO - 下载公司新闻数据: NVDA (2025-04-30)
2025-07-07 17:06:26,832 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:26,832 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:06:26,868 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:27,261 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-30.json
2025-07-07 17:06:27,262 - INFO - 下载市值数据: NVDA (2025-04-30)
2025-07-07 17:06:27,764 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-30.json
2025-07-07 17:06:27,765 - INFO - 下载财务科目数据: NVDA (2025-04-30)
2025-07-07 17:06:28,402 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:29,271 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:29,272 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:29,273 - INFO - 下载财务指标数据: NVDA (2025-05-07)
2025-07-07 17:06:29,776 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-07.json
2025-07-07 17:06:29,777 - INFO - 下载内幕交易数据: NVDA (2025-05-07)
2025-07-07 17:06:30,280 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-07.json
2025-07-07 17:06:30,280 - INFO - 下载公司新闻数据: NVDA (2025-05-07)
2025-07-07 17:06:30,784 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-07.json
2025-07-07 17:06:30,784 - INFO - 下载市值数据: NVDA (2025-05-07)
2025-07-07 17:06:31,286 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-07.json
2025-07-07 17:06:31,286 - INFO - 下载财务科目数据: NVDA (2025-05-07)
2025-07-07 17:06:31,926 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:31,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:31,967 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:31,968 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:32,819 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:32,819 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:32,819 - INFO - 下载财务指标数据: NVDA (2025-05-14)
2025-07-07 17:06:33,322 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-14.json
2025-07-07 17:06:33,323 - INFO - 下载内幕交易数据: NVDA (2025-05-14)
2025-07-07 17:06:33,827 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-14.json
2025-07-07 17:06:33,828 - INFO - 下载公司新闻数据: NVDA (2025-05-14)
2025-07-07 17:06:34,331 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-14.json
2025-07-07 17:06:34,331 - INFO - 下载市值数据: NVDA (2025-05-14)
2025-07-07 17:06:34,834 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-14.json
2025-07-07 17:06:34,835 - INFO - 下载财务科目数据: NVDA (2025-05-14)
2025-07-07 17:06:34,868 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:34,868 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:06:34,902 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:35,468 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:35,501 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:35,502 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:35,502 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:35,535 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:39,536 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:39,536 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:06:39,578 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:47,579 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:47,579 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:06:47,617 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:50,902 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:50,902 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:06:50,939 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:53,974 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:53,974 - DEBUG - Starting new HTTPS connection (18): api.financialdatasets.ai:443
2025-07-07 17:06:54,016 - ERROR - 下载财务科目数据失败 MSFT: HTTPSConnectionPool(host='api.financialdatasets.ai', port=443): Max retries exceeded with url: /financials/search/line-items (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
2025-07-07 17:06:54,016 - INFO - 下载财务指标数据: MSFT (2025-03-26)
2025-07-07 17:06:54,519 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-26.json
2025-07-07 17:06:54,520 - INFO - 下载内幕交易数据: MSFT (2025-03-26)
2025-07-07 17:06:55,023 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-26.json
2025-07-07 17:06:55,024 - INFO - 下载公司新闻数据: MSFT (2025-03-26)
2025-07-07 17:06:55,529 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-26.json
2025-07-07 17:06:55,530 - INFO - 下载市值数据: MSFT (2025-03-26)
2025-07-07 17:06:56,032 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-26.json
2025-07-07 17:06:56,032 - INFO - 下载财务科目数据: MSFT (2025-03-26)
2025-07-07 17:06:56,664 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:56,708 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:56,708 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:56,708 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:57,607 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:57,608 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:57,609 - INFO - 下载财务指标数据: MSFT (2025-04-02)
2025-07-07 17:06:58,111 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-02.json
2025-07-07 17:06:58,112 - INFO - 下载内幕交易数据: MSFT (2025-04-02)
2025-07-07 17:06:58,613 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-02.json
2025-07-07 17:06:58,613 - INFO - 下载公司新闻数据: MSFT (2025-04-02)
2025-07-07 17:06:59,118 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-02.json
2025-07-07 17:06:59,119 - INFO - 下载市值数据: MSFT (2025-04-02)
2025-07-07 17:06:59,621 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-02.json
2025-07-07 17:06:59,624 - INFO - 下载财务科目数据: MSFT (2025-04-02)
2025-07-07 17:07:00,258 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:00,301 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:00,301 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:00,301 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:01,212 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:01,213 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:01,213 - INFO - 下载财务指标数据: MSFT (2025-04-09)
2025-07-07 17:07:01,714 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-09.json
2025-07-07 17:07:01,715 - INFO - 下载内幕交易数据: MSFT (2025-04-09)
2025-07-07 17:07:02,218 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-09.json
2025-07-07 17:07:02,219 - INFO - 下载公司新闻数据: MSFT (2025-04-09)
2025-07-07 17:07:02,726 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-09.json
2025-07-07 17:07:02,726 - INFO - 下载市值数据: MSFT (2025-04-09)
2025-07-07 17:07:03,229 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-09.json
2025-07-07 17:07:03,229 - INFO - 下载财务科目数据: MSFT (2025-04-09)
2025-07-07 17:07:03,618 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:03,618 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:07:03,655 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:03,862 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:04,736 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:04,737 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:04,739 - INFO - 下载财务指标数据: MSFT (2025-04-16)
2025-07-07 17:07:05,241 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-16.json
2025-07-07 17:07:05,242 - INFO - 下载内幕交易数据: MSFT (2025-04-16)
2025-07-07 17:07:05,744 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-16.json
2025-07-07 17:07:05,744 - INFO - 下载公司新闻数据: MSFT (2025-04-16)
2025-07-07 17:07:06,246 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-16.json
2025-07-07 17:07:06,246 - INFO - 下载市值数据: MSFT (2025-04-16)
2025-07-07 17:07:06,747 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-16.json
2025-07-07 17:07:06,747 - INFO - 下载财务科目数据: MSFT (2025-04-16)
2025-07-07 17:07:07,381 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:07,422 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:07,422 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:07,422 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:07,474 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:11,475 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:11,475 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:11,513 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:19,513 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:19,514 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:07:19,555 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:22,940 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:22,940 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:07:23,985 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:07:24,021 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:24,021 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:24,022 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:07:24,074 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:28,076 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:28,076 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:07:28,118 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:35,556 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:35,557 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:07:35,597 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:35,656 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:35,657 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:07:36,119 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:36,119 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:07:36,699 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:36,700 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:36,700 - INFO - 下载财务指标数据: NVDA (2025-05-21)
2025-07-07 17:07:36,972 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:36,972 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:36,973 - INFO - 下载财务指标数据: AAPL (2025-05-14)
2025-07-07 17:07:37,202 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-21.json
2025-07-07 17:07:37,203 - INFO - 下载内幕交易数据: NVDA (2025-05-21)
2025-07-07 17:07:37,475 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-14.json
2025-07-07 17:07:37,475 - INFO - 下载内幕交易数据: AAPL (2025-05-14)
2025-07-07 17:07:37,706 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-21.json
2025-07-07 17:07:37,708 - INFO - 下载公司新闻数据: NVDA (2025-05-21)
2025-07-07 17:07:37,979 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-14.json
2025-07-07 17:07:37,980 - INFO - 下载公司新闻数据: AAPL (2025-05-14)
2025-07-07 17:07:38,212 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-21.json
2025-07-07 17:07:38,212 - INFO - 下载市值数据: NVDA (2025-05-21)
2025-07-07 17:07:38,482 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-14.json
2025-07-07 17:07:38,484 - INFO - 下载市值数据: AAPL (2025-05-14)
2025-07-07 17:07:38,714 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-21.json
2025-07-07 17:07:38,715 - INFO - 下载财务科目数据: NVDA (2025-05-21)
2025-07-07 17:07:38,986 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-14.json
2025-07-07 17:07:38,986 - INFO - 下载财务科目数据: AAPL (2025-05-14)
2025-07-07 17:07:39,346 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:39,383 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:39,383 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:39,383 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:39,445 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:39,616 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:39,660 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:39,660 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:39,660 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:39,699 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:43,447 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:43,447 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:43,700 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:43,700 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:43,739 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:44,273 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:44,274 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:44,275 - INFO - 下载财务指标数据: NVDA (2025-05-28)
2025-07-07 17:07:44,777 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-28.json
2025-07-07 17:07:44,777 - INFO - 下载内幕交易数据: NVDA (2025-05-28)
2025-07-07 17:07:45,281 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-28.json
2025-07-07 17:07:45,282 - INFO - 下载公司新闻数据: NVDA (2025-05-28)
2025-07-07 17:07:45,785 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-28.json
2025-07-07 17:07:45,785 - INFO - 下载市值数据: NVDA (2025-05-28)
2025-07-07 17:07:46,287 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-28.json
2025-07-07 17:07:46,287 - INFO - 下载财务科目数据: NVDA (2025-05-28)
2025-07-07 17:07:46,922 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:47,032 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:47,032 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:47,032 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:48,924 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:48,924 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:48,925 - INFO - 下载财务指标数据: NVDA (2025-06-01)
2025-07-07 17:07:49,428 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-06-01.json
2025-07-07 17:07:49,428 - INFO - 下载内幕交易数据: NVDA (2025-06-01)
2025-07-07 17:07:49,930 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-06-01.json
2025-07-07 17:07:49,930 - INFO - 下载公司新闻数据: NVDA (2025-06-01)
2025-07-07 17:07:50,432 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-06-01.json
2025-07-07 17:07:50,433 - INFO - 下载市值数据: NVDA (2025-06-01)
2025-07-07 17:07:50,935 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-06-01.json
2025-07-07 17:07:50,935 - INFO - 下载财务科目数据: NVDA (2025-06-01)
2025-07-07 17:07:51,566 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:51,724 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:51,724 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:51,724 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:51,740 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:51,740 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:07:51,789 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:52,621 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:52,622 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:52,623 - INFO - 下载财务指标数据: AAPL (2025-05-21)
2025-07-07 17:07:53,125 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-21.json
2025-07-07 17:07:53,126 - INFO - 下载内幕交易数据: AAPL (2025-05-21)
2025-07-07 17:07:53,629 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-21.json
2025-07-07 17:07:53,629 - INFO - 下载公司新闻数据: AAPL (2025-05-21)
2025-07-07 17:07:54,135 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-21.json
2025-07-07 17:07:54,135 - INFO - 下载市值数据: AAPL (2025-05-21)
2025-07-07 17:07:54,636 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-21.json
2025-07-07 17:07:54,637 - INFO - 下载财务科目数据: AAPL (2025-05-21)
2025-07-07 17:07:55,267 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:55,299 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:55,300 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:55,300 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:55,791 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:55,792 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:55,837 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:56,172 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:56,173 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:56,174 - INFO - 下载财务指标数据: AAPL (2025-05-28)
2025-07-07 17:07:56,678 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-28.json
2025-07-07 17:07:56,679 - INFO - 下载内幕交易数据: AAPL (2025-05-28)
2025-07-07 17:07:57,182 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-28.json
2025-07-07 17:07:57,184 - INFO - 下载公司新闻数据: AAPL (2025-05-28)
2025-07-07 17:07:57,686 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-28.json
2025-07-07 17:07:57,687 - INFO - 下载市值数据: AAPL (2025-05-28)
2025-07-07 17:07:58,189 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-28.json
2025-07-07 17:07:58,190 - INFO - 下载财务科目数据: AAPL (2025-05-28)
2025-07-07 17:07:58,820 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:58,877 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:58,878 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:58,878 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:58,919 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:02,920 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:02,920 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:08:02,961 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:03,838 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:03,839 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:08:03,909 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:07,598 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:07,598 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:08:08,636 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:08:08,672 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:08,674 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:08,674 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:08:08,713 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:10,961 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:10,963 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:08:11,002 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:12,713 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:12,714 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:08:12,758 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:19,910 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:19,910 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:08:19,948 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:20,759 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:20,760 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:08:20,794 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:27,003 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:27,003 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:08:27,047 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:36,796 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:36,797 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:08:36,842 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:51,949 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:51,949 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:08:52,987 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:08:53,026 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:53,026 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:53,027 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:08:53,062 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:57,064 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:57,064 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:08:57,115 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:59,048 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:59,048 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:09:00,103 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:09:00,147 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:00,147 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:00,147 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:09:00,186 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:04,186 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:04,187 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:09:04,219 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:05,116 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:05,117 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:09:05,169 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:08,843 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:08,843 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:09:10,885 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:09:11,732 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:11,733 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:11,733 - INFO - 下载财务指标数据: MSFT (2025-04-23)
2025-07-07 17:09:12,220 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:12,221 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:09:12,234 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-23.json
2025-07-07 17:09:12,235 - INFO - 下载内幕交易数据: MSFT (2025-04-23)
2025-07-07 17:09:12,262 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:12,737 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-23.json
2025-07-07 17:09:12,737 - INFO - 下载公司新闻数据: MSFT (2025-04-23)
2025-07-07 17:09:13,242 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-23.json
2025-07-07 17:09:13,242 - INFO - 下载市值数据: MSFT (2025-04-23)
2025-07-07 17:09:13,746 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-23.json
2025-07-07 17:09:13,747 - INFO - 下载财务科目数据: MSFT (2025-04-23)
2025-07-07 17:09:14,377 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:14,418 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:14,418 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:14,418 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:14,463 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:18,464 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:18,464 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:19,304 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:19,305 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:19,305 - INFO - 下载财务指标数据: MSFT (2025-04-30)
2025-07-07 17:09:19,808 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-30.json
2025-07-07 17:09:19,809 - INFO - 下载内幕交易数据: MSFT (2025-04-30)
2025-07-07 17:09:20,312 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-30.json
2025-07-07 17:09:20,312 - INFO - 下载公司新闻数据: MSFT (2025-04-30)
2025-07-07 17:09:20,818 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-30.json
2025-07-07 17:09:20,818 - INFO - 下载市值数据: MSFT (2025-04-30)
2025-07-07 17:09:21,171 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:21,171 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:09:21,319 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-30.json
2025-07-07 17:09:21,319 - INFO - 下载财务科目数据: MSFT (2025-04-30)
2025-07-07 17:09:21,956 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:22,000 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:22,000 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:22,000 - INFO - 完成下载: NVDA
2025-07-07 17:09:22,784 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:22,785 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:22,787 - INFO - 下载财务指标数据: MSFT (2025-05-07)
2025-07-07 17:09:23,291 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-07.json
2025-07-07 17:09:23,292 - INFO - 下载内幕交易数据: MSFT (2025-05-07)
2025-07-07 17:09:23,794 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-07.json
2025-07-07 17:09:23,794 - INFO - 下载公司新闻数据: MSFT (2025-05-07)
2025-07-07 17:09:24,300 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-07.json
2025-07-07 17:09:24,301 - INFO - 下载市值数据: MSFT (2025-05-07)
2025-07-07 17:09:24,802 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-07.json
2025-07-07 17:09:24,802 - INFO - 下载财务科目数据: MSFT (2025-05-07)
2025-07-07 17:09:25,436 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:25,475 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:25,476 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:25,476 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:25,514 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:28,263 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:28,264 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:09:28,304 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:29,514 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:29,515 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:30,363 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:30,363 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:30,364 - INFO - 下载财务指标数据: MSFT (2025-05-14)
2025-07-07 17:09:30,866 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-14.json
2025-07-07 17:09:30,867 - INFO - 下载内幕交易数据: MSFT (2025-05-14)
2025-07-07 17:09:31,370 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-14.json
2025-07-07 17:09:31,372 - INFO - 下载公司新闻数据: MSFT (2025-05-14)
2025-07-07 17:09:31,874 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-14.json
2025-07-07 17:09:31,874 - INFO - 下载市值数据: MSFT (2025-05-14)
2025-07-07 17:09:32,376 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-14.json
2025-07-07 17:09:32,377 - INFO - 下载财务科目数据: MSFT (2025-05-14)
2025-07-07 17:09:33,004 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:33,043 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:33,044 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:33,044 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:33,079 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:37,080 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:37,081 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:37,936 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:37,936 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:37,937 - INFO - 下载财务指标数据: MSFT (2025-05-21)
2025-07-07 17:09:38,441 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-21.json
2025-07-07 17:09:38,441 - INFO - 下载内幕交易数据: MSFT (2025-05-21)
2025-07-07 17:09:38,946 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-21.json
2025-07-07 17:09:38,947 - INFO - 下载公司新闻数据: MSFT (2025-05-21)
2025-07-07 17:09:39,450 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-21.json
2025-07-07 17:09:39,452 - INFO - 下载市值数据: MSFT (2025-05-21)
2025-07-07 17:09:39,953 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-21.json
2025-07-07 17:09:39,954 - INFO - 下载财务科目数据: MSFT (2025-05-21)
2025-07-07 17:09:40,582 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:40,625 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:40,625 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:40,626 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:41,500 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:41,500 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:41,501 - INFO - 下载财务指标数据: MSFT (2025-05-28)
2025-07-07 17:09:42,003 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-28.json
2025-07-07 17:09:42,004 - INFO - 下载内幕交易数据: MSFT (2025-05-28)
2025-07-07 17:09:42,508 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-28.json
2025-07-07 17:09:42,509 - INFO - 下载公司新闻数据: MSFT (2025-05-28)
2025-07-07 17:09:43,014 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-28.json
2025-07-07 17:09:43,015 - INFO - 下载市值数据: MSFT (2025-05-28)
2025-07-07 17:09:43,516 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-28.json
2025-07-07 17:09:43,516 - INFO - 下载财务科目数据: MSFT (2025-05-28)
2025-07-07 17:09:44,152 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:44,187 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:44,188 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:44,188 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:44,221 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:48,222 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:48,222 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:48,270 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:56,271 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:56,271 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:09:56,311 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:00,305 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:00,306 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:10:02,345 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:10:02,380 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:02,381 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:02,381 - DEBUG - Starting new HTTPS connection (14): api.financialdatasets.ai:443
2025-07-07 17:10:02,415 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:06,416 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:06,416 - DEBUG - Starting new HTTPS connection (15): api.financialdatasets.ai:443
2025-07-07 17:10:06,456 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:12,312 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:12,312 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:10:13,160 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:13,160 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:13,160 - INFO - 下载财务指标数据: MSFT (2025-06-01)
2025-07-07 17:10:13,663 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-06-01.json
2025-07-07 17:10:13,664 - INFO - 下载内幕交易数据: MSFT (2025-06-01)
2025-07-07 17:10:14,167 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-06-01.json
2025-07-07 17:10:14,168 - INFO - 下载公司新闻数据: MSFT (2025-06-01)
2025-07-07 17:10:14,458 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:14,459 - DEBUG - Starting new HTTPS connection (16): api.financialdatasets.ai:443
2025-07-07 17:10:14,674 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-06-01.json
2025-07-07 17:10:14,674 - INFO - 下载市值数据: MSFT (2025-06-01)
2025-07-07 17:10:15,175 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-06-01.json
2025-07-07 17:10:15,176 - INFO - 下载财务科目数据: MSFT (2025-06-01)
2025-07-07 17:10:15,404 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:15,405 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:15,407 - INFO - 下载财务指标数据: AAPL (2025-06-01)
2025-07-07 17:10:15,811 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:10:15,855 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:15,856 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:15,857 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:10:15,899 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:15,910 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-06-01.json
2025-07-07 17:10:15,911 - INFO - 下载内幕交易数据: AAPL (2025-06-01)
2025-07-07 17:10:16,413 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-06-01.json
2025-07-07 17:10:16,414 - INFO - 下载公司新闻数据: AAPL (2025-06-01)
2025-07-07 17:10:16,919 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-06-01.json
2025-07-07 17:10:16,920 - INFO - 下载市值数据: AAPL (2025-06-01)
2025-07-07 17:10:17,421 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-06-01.json
2025-07-07 17:10:17,422 - INFO - 下载财务科目数据: AAPL (2025-06-01)
2025-07-07 17:10:18,055 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:10:18,089 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:18,090 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:18,090 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:10:19,002 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:19,002 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:19,002 - INFO - 完成下载: AAPL
2025-07-07 17:10:19,900 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:19,900 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:10:21,896 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:21,897 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:21,897 - INFO - 完成下载: MSFT
2025-07-07 17:10:21,902 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_171021.txt
2025-07-07 17:24:49,633 - INFO - 开始下载数据: 1 个股票, 1 种数据类型
2025-07-07 17:24:49,634 - INFO - 时间范围: 2025-01-01 到 2025-01-07
2025-07-07 17:24:49,634 - INFO - 数据类型: line_items
2025-07-07 17:24:49,634 - INFO - AAPL line_items: 生成 2 个采样日期
2025-07-07 17:24:49,635 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 17:24:50,274 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:24:51,180 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:24:51,183 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-01.json
2025-07-07 17:24:51,184 - INFO - 下载财务科目数据: AAPL (2025-01-07)
2025-07-07 17:24:51,816 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:24:51,909 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:24:51,910 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:24:51,910 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:24:52,783 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:24:52,784 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-07.json
2025-07-07 17:24:52,785 - INFO - 完成下载: AAPL
2025-07-07 17:24:52,786 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_172452.txt
2025-07-07 17:25:12,454 - INFO - 开始下载数据: 1 个股票, 6 种数据类型
2025-07-07 17:25:12,454 - INFO - 时间范围: 2025-01-01 到 2025-01-10
2025-07-07 17:25:12,454 - INFO - 数据类型: prices, market_cap, company_news, financial_metrics, line_items, insider_trades
2025-07-07 17:25:12,456 - INFO - 下载股价数据: AAPL (2025-01-01 to 2025-01-10)
2025-07-07 17:25:13,090 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:25:13,174 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:13,175 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10
2025-07-07 17:25:13,176 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:25:13,258 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:17,259 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10
2025-07-07 17:25:17,259 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:25:19,545 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10 HTTP/1.1" 200 None
2025-07-07 17:25:19,552 - DEBUG - 数据已保存到: financial_data_offline\AAPL_prices\AAPL_prices_2025-01-01_to_2025-01-10.json
2025-07-07 17:25:19,552 - INFO - AAPL financial_metrics: 生成 2 个采样日期
2025-07-07 17:25:19,552 - INFO - 财务指标数据已存在: AAPL (2025-01-01)
2025-07-07 17:25:19,553 - INFO - 下载财务指标数据: AAPL (2025-01-10)
2025-07-07 17:25:20,186 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:25:20,653 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:20,653 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:20,653 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:25:20,736 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:24,737 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:24,738 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:25:25,098 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:33,099 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:33,099 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:25:33,381 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:49,382 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:49,382 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:25:51,158 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:26:23,159 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:26:23,160 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:26:24,772 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 17:26:25,052 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-10.json
2025-07-07 17:26:25,053 - INFO - AAPL insider_trades: 生成 3 个采样日期
2025-07-07 17:26:25,053 - INFO - 内幕交易数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:25,054 - INFO - 内幕交易数据已存在: AAPL (2025-01-08)
2025-07-07 17:26:25,054 - INFO - 下载内幕交易数据: AAPL (2025-01-10)
2025-07-07 17:26:25,687 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:26:27,486 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-10&limit=50 HTTP/1.1" 200 None
2025-07-07 17:26:27,922 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-10.json
2025-07-07 17:26:27,923 - INFO - AAPL company_news: 生成 8 个采样日期
2025-07-07 17:26:27,924 - INFO - 公司新闻数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:27,924 - INFO - 下载公司新闻数据: AAPL (2025-01-02)
2025-07-07 17:26:28,567 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:26:28,644 - DEBUG - Incremented Retry for (url='/news/?ticker=AAPL&end_date=2025-01-02&limit=100'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:26:28,644 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=AAPL&end_date=2025-01-02&limit=100
2025-07-07 17:26:28,644 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:26:29,757 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=AAPL&end_date=2025-01-02&limit=100 HTTP/1.1" 200 None
2025-07-07 17:26:30,204 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-02.json
2025-07-07 17:26:30,205 - INFO - 下载公司新闻数据: AAPL (2025-01-03)
2025-07-07 17:26:30,710 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-03.json
2025-07-07 17:26:30,711 - INFO - 下载公司新闻数据: AAPL (2025-01-06)
2025-07-07 17:26:31,216 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-06.json
2025-07-07 17:26:31,217 - INFO - 下载公司新闻数据: AAPL (2025-01-07)
2025-07-07 17:26:31,720 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-07.json
2025-07-07 17:26:31,721 - INFO - 公司新闻数据已存在: AAPL (2025-01-08)
2025-07-07 17:26:31,722 - INFO - 下载公司新闻数据: AAPL (2025-01-09)
2025-07-07 17:26:32,226 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-09.json
2025-07-07 17:26:32,227 - INFO - 下载公司新闻数据: AAPL (2025-01-10)
2025-07-07 17:26:32,732 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-10.json
2025-07-07 17:26:32,732 - INFO - AAPL market_cap: 生成 8 个采样日期
2025-07-07 17:26:32,733 - INFO - 市值数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:32,733 - INFO - 下载市值数据: AAPL (2025-01-02)
2025-07-07 17:26:33,236 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-02.json
2025-07-07 17:26:33,236 - INFO - 下载市值数据: AAPL (2025-01-03)
2025-07-07 17:26:33,737 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-03.json
2025-07-07 17:26:33,738 - INFO - 下载市值数据: AAPL (2025-01-06)
2025-07-07 17:26:34,240 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-06.json
2025-07-07 17:26:34,241 - INFO - 下载市值数据: AAPL (2025-01-07)
2025-07-07 17:26:34,743 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-07.json
2025-07-07 17:26:34,743 - INFO - 市值数据已存在: AAPL (2025-01-08)
2025-07-07 17:26:34,743 - INFO - 下载市值数据: AAPL (2025-01-09)
2025-07-07 17:26:35,244 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-09.json
2025-07-07 17:26:35,245 - INFO - 下载市值数据: AAPL (2025-01-10)
2025-07-07 17:26:35,746 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-10.json
2025-07-07 17:26:35,747 - INFO - AAPL line_items: 生成 2 个采样日期
2025-07-07 17:26:35,747 - INFO - 财务科目数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:35,747 - INFO - 下载财务科目数据: AAPL (2025-01-10)
2025-07-07 17:26:36,382 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:26:36,454 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:26:36,455 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:26:36,455 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:26:37,431 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:26:37,432 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-10.json
2025-07-07 17:26:37,432 - INFO - 完成下载: AAPL
2025-07-07 17:26:37,434 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_172637.txt
2025-07-07 17:33:25,818 - INFO - 开始下载数据: 3 个股票, 6 种数据类型
2025-07-07 17:33:25,818 - INFO - 时间范围: 2025-01-01 到 2025-06-01
2025-07-07 17:33:25,818 - INFO - 数据类型: prices, financial_metrics, insider_trades, company_news, market_cap, line_items
2025-07-07 17:33:25,819 - INFO - 下载股价数据: AAPL (2025-01-01 to 2025-06-01)
2025-07-07 17:33:25,819 - INFO - 下载股价数据: MSFT (2025-01-01 to 2025-06-01)
2025-07-07 17:33:25,819 - INFO - 下载股价数据: NVDA (2025-01-01 to 2025-06-01)
2025-07-07 17:33:26,550 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:26,560 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:26,568 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:26,602 - DEBUG - Incremented Retry for (url='/prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:26,603 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:26,603 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:33:26,628 - DEBUG - Incremented Retry for (url='/prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:26,628 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:26,628 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:33:26,681 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:26,682 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:26,682 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:33:26,722 - DEBUG - Incremented Retry for (url='/prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:26,732 - DEBUG - Incremented Retry for (url='/prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:26,781 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:30,723 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:30,723 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:33:30,732 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:30,733 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:33:30,782 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:30,782 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:33:30,817 - DEBUG - Incremented Retry for (url='/prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:30,825 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:30,837 - DEBUG - Incremented Retry for (url='/prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:38,817 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:38,817 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:33:38,827 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:38,827 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:33:38,838 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:38,839 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:33:38,895 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:38,967 - DEBUG - Incremented Retry for (url='/prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:39,885 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=MSFT&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 HTTP/1.1" 200 None
2025-07-07 17:33:40,104 - DEBUG - 数据已保存到: financial_data_offline\MSFT_prices\MSFT_prices_2025-01-01_to_2025-06-01.json
2025-07-07 17:33:40,105 - INFO - MSFT financial_metrics: 生成 6 个采样日期
2025-07-07 17:33:40,106 - INFO - 下载财务指标数据: MSFT (2025-01-01)
2025-07-07 17:33:40,735 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:41,728 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 17:33:41,942 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-01.json
2025-07-07 17:33:41,942 - INFO - 下载财务指标数据: MSFT (2025-02-01)
2025-07-07 17:33:42,445 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-01.json
2025-07-07 17:33:42,445 - INFO - 下载财务指标数据: MSFT (2025-03-01)
2025-07-07 17:33:42,947 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-01.json
2025-07-07 17:33:42,949 - INFO - 下载财务指标数据: MSFT (2025-04-01)
2025-07-07 17:33:43,452 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-01.json
2025-07-07 17:33:43,452 - INFO - 下载财务指标数据: MSFT (2025-05-01)
2025-07-07 17:33:43,955 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-01.json
2025-07-07 17:33:43,956 - INFO - 下载财务指标数据: MSFT (2025-06-01)
2025-07-07 17:33:44,460 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-06-01.json
2025-07-07 17:33:44,461 - INFO - MSFT insider_trades: 生成 23 个采样日期
2025-07-07 17:33:44,461 - INFO - 下载内幕交易数据: MSFT (2025-01-01)
2025-07-07 17:33:45,097 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:45,133 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:45,133 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:33:45,133 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:33:45,250 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:49,251 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:33:49,252 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:33:49,335 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:54,896 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:54,897 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:33:54,968 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 17:33:54,968 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:33:56,071 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 HTTP/1.1" 200 None
2025-07-07 17:33:56,163 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 HTTP/1.1" 200 None
2025-07-07 17:33:56,293 - DEBUG - 数据已保存到: financial_data_offline\AAPL_prices\AAPL_prices_2025-01-01_to_2025-06-01.json
2025-07-07 17:33:56,293 - INFO - AAPL financial_metrics: 生成 6 个采样日期
2025-07-07 17:33:56,294 - INFO - 下载财务指标数据: AAPL (2025-01-01)
2025-07-07 17:33:56,376 - DEBUG - 数据已保存到: financial_data_offline\NVDA_prices\NVDA_prices_2025-01-01_to_2025-06-01.json
2025-07-07 17:33:56,376 - INFO - NVDA financial_metrics: 生成 6 个采样日期
2025-07-07 17:33:56,376 - INFO - 下载财务指标数据: NVDA (2025-01-01)
2025-07-07 17:33:56,953 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:57,031 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:33:57,046 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:57,046 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 17:33:57,046 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:33:57,127 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:57,132 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=NVDA&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:33:57,132 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=NVDA&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 17:33:57,133 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:33:57,336 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:33:57,337 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:33:58,115 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=NVDA&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 17:33:58,308 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50 HTTP/1.1" 200 None
2025-07-07 17:33:58,330 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-01.json
2025-07-07 17:33:58,330 - INFO - 下载财务指标数据: NVDA (2025-02-01)
2025-07-07 17:33:58,528 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-01.json
2025-07-07 17:33:58,529 - INFO - 下载内幕交易数据: MSFT (2025-01-08)
2025-07-07 17:33:58,834 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-01.json
2025-07-07 17:33:58,835 - INFO - 下载财务指标数据: NVDA (2025-03-01)
2025-07-07 17:33:59,033 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-08.json
2025-07-07 17:33:59,034 - INFO - 下载内幕交易数据: MSFT (2025-01-15)
2025-07-07 17:33:59,338 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-01.json
2025-07-07 17:33:59,339 - INFO - 下载财务指标数据: NVDA (2025-04-01)
2025-07-07 17:33:59,535 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-15.json
2025-07-07 17:33:59,535 - INFO - 下载内幕交易数据: MSFT (2025-01-22)
2025-07-07 17:33:59,842 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-01.json
2025-07-07 17:33:59,842 - INFO - 下载财务指标数据: NVDA (2025-05-01)
2025-07-07 17:34:00,037 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-22.json
2025-07-07 17:34:00,037 - INFO - 下载内幕交易数据: MSFT (2025-01-29)
2025-07-07 17:34:00,345 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-01.json
2025-07-07 17:34:00,346 - INFO - 下载财务指标数据: NVDA (2025-06-01)
2025-07-07 17:34:00,540 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-29.json
2025-07-07 17:34:00,542 - INFO - 下载内幕交易数据: MSFT (2025-02-05)
2025-07-07 17:34:00,848 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-06-01.json
2025-07-07 17:34:00,849 - INFO - NVDA insider_trades: 生成 23 个采样日期
2025-07-07 17:34:00,849 - INFO - 下载内幕交易数据: NVDA (2025-01-01)
2025-07-07 17:34:01,044 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-05.json
2025-07-07 17:34:01,044 - INFO - 下载内幕交易数据: MSFT (2025-02-12)
2025-07-07 17:34:01,128 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 17:34:01,129 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:34:01,236 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:01,498 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:34:01,546 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-12.json
2025-07-07 17:34:01,546 - INFO - 下载内幕交易数据: MSFT (2025-02-19)
2025-07-07 17:34:01,585 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:01,585 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:34:01,585 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:34:01,854 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:02,047 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-19.json
2025-07-07 17:34:02,047 - INFO - 下载内幕交易数据: MSFT (2025-02-26)
2025-07-07 17:34:02,549 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-26.json
2025-07-07 17:34:02,550 - INFO - 下载内幕交易数据: MSFT (2025-03-05)
2025-07-07 17:34:03,053 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-05.json
2025-07-07 17:34:03,054 - INFO - 下载内幕交易数据: MSFT (2025-03-12)
2025-07-07 17:34:03,557 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-12.json
2025-07-07 17:34:03,558 - INFO - 下载内幕交易数据: MSFT (2025-03-19)
2025-07-07 17:34:04,061 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-19.json
2025-07-07 17:34:04,062 - INFO - 下载内幕交易数据: MSFT (2025-03-26)
2025-07-07 17:34:04,564 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-26.json
2025-07-07 17:34:04,565 - INFO - 下载内幕交易数据: MSFT (2025-04-02)
2025-07-07 17:34:05,069 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-02.json
2025-07-07 17:34:05,069 - INFO - 下载内幕交易数据: MSFT (2025-04-09)
2025-07-07 17:34:05,573 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-09.json
2025-07-07 17:34:05,575 - INFO - 下载内幕交易数据: MSFT (2025-04-16)
2025-07-07 17:34:05,855 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:34:05,856 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:34:06,076 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-16.json
2025-07-07 17:34:06,077 - INFO - 下载内幕交易数据: MSFT (2025-04-23)
2025-07-07 17:34:06,186 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:06,578 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-23.json
2025-07-07 17:34:06,580 - INFO - 下载内幕交易数据: MSFT (2025-04-30)
2025-07-07 17:34:07,083 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-30.json
2025-07-07 17:34:07,084 - INFO - 下载内幕交易数据: MSFT (2025-05-07)
2025-07-07 17:34:07,585 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-07.json
2025-07-07 17:34:07,586 - INFO - 下载内幕交易数据: MSFT (2025-05-14)
2025-07-07 17:34:08,088 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-14.json
2025-07-07 17:34:08,090 - INFO - 下载内幕交易数据: MSFT (2025-05-21)
2025-07-07 17:34:08,593 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-21.json
2025-07-07 17:34:08,594 - INFO - 下载内幕交易数据: MSFT (2025-05-28)
2025-07-07 17:34:09,097 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-28.json
2025-07-07 17:34:09,098 - INFO - 下载内幕交易数据: MSFT (2025-06-01)
2025-07-07 17:34:09,237 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 17:34:09,237 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:34:09,601 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-06-01.json
2025-07-07 17:34:09,602 - INFO - MSFT company_news: 生成 109 个采样日期
2025-07-07 17:34:09,603 - INFO - 下载公司新闻数据: MSFT (2025-01-01)
2025-07-07 17:34:10,224 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 17:34:10,237 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:34:10,391 - DEBUG - Incremented Retry for (url='/news/?ticker=MSFT&end_date=2025-01-01&limit=100'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:10,391 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=MSFT&end_date=2025-01-01&limit=100
2025-07-07 17:34:10,391 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:34:10,444 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-01.json
2025-07-07 17:34:10,444 - INFO - 下载财务指标数据: AAPL (2025-02-01)
2025-07-07 17:34:10,488 - DEBUG - Incremented Retry for (url='/news/?ticker=MSFT&end_date=2025-01-01&limit=100'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:10,946 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-01.json
2025-07-07 17:34:10,946 - INFO - 下载财务指标数据: AAPL (2025-03-01)
2025-07-07 17:34:11,448 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-01.json
2025-07-07 17:34:11,449 - INFO - 下载财务指标数据: AAPL (2025-04-01)
2025-07-07 17:34:11,952 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-01.json
2025-07-07 17:34:11,954 - INFO - 下载财务指标数据: AAPL (2025-05-01)
2025-07-07 17:34:12,457 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-01.json
2025-07-07 17:34:12,458 - INFO - 下载财务指标数据: AAPL (2025-06-01)
2025-07-07 17:34:12,960 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-06-01.json
2025-07-07 17:34:12,962 - INFO - AAPL insider_trades: 生成 23 个采样日期
2025-07-07 17:34:12,962 - INFO - 下载内幕交易数据: AAPL (2025-01-01)
2025-07-07 17:34:13,615 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:34:14,188 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:34:14,189 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:34:14,293 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:14,489 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=MSFT&end_date=2025-01-01&limit=100
2025-07-07 17:34:14,489 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:34:14,531 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-01&limit=50 HTTP/1.1" 200 None
2025-07-07 17:34:14,744 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-01.json
2025-07-07 17:34:14,745 - INFO - 下载内幕交易数据: AAPL (2025-01-08)
2025-07-07 17:34:15,248 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-08.json
2025-07-07 17:34:15,249 - INFO - 下载内幕交易数据: AAPL (2025-01-15)
2025-07-07 17:34:15,724 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=MSFT&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 17:34:15,750 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-15.json
2025-07-07 17:34:15,750 - INFO - 下载内幕交易数据: AAPL (2025-01-22)
2025-07-07 17:34:16,176 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-01.json
2025-07-07 17:34:16,176 - INFO - 下载公司新闻数据: MSFT (2025-01-02)
2025-07-07 17:34:16,253 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-22.json
2025-07-07 17:34:16,254 - INFO - 下载内幕交易数据: AAPL (2025-01-29)
2025-07-07 17:34:16,680 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-02.json
2025-07-07 17:34:16,681 - INFO - 下载公司新闻数据: MSFT (2025-01-03)
2025-07-07 17:34:16,756 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-29.json
2025-07-07 17:34:16,757 - INFO - 下载内幕交易数据: AAPL (2025-02-05)
2025-07-07 17:34:17,184 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-03.json
2025-07-07 17:34:17,185 - INFO - 下载公司新闻数据: MSFT (2025-01-06)
2025-07-07 17:34:17,260 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-05.json
2025-07-07 17:34:17,260 - INFO - 下载内幕交易数据: AAPL (2025-02-12)
2025-07-07 17:34:17,690 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-06.json
2025-07-07 17:34:17,692 - INFO - 下载公司新闻数据: MSFT (2025-01-07)
2025-07-07 17:34:17,764 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-12.json
2025-07-07 17:34:17,764 - INFO - 下载内幕交易数据: AAPL (2025-02-19)
2025-07-07 17:34:18,196 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-07.json
2025-07-07 17:34:18,197 - INFO - 下载公司新闻数据: MSFT (2025-01-08)
2025-07-07 17:34:18,267 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-19.json
2025-07-07 17:34:18,267 - INFO - 下载内幕交易数据: AAPL (2025-02-26)
2025-07-07 17:34:18,702 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-08.json
2025-07-07 17:34:18,702 - INFO - 下载公司新闻数据: MSFT (2025-01-09)
2025-07-07 17:34:18,770 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-26.json
2025-07-07 17:34:18,771 - INFO - 下载内幕交易数据: AAPL (2025-03-05)
2025-07-07 17:34:19,207 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-09.json
2025-07-07 17:34:19,208 - INFO - 下载公司新闻数据: MSFT (2025-01-10)
2025-07-07 17:34:19,276 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-05.json
2025-07-07 17:34:19,277 - INFO - 下载内幕交易数据: AAPL (2025-03-12)
2025-07-07 17:34:19,711 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-10.json
2025-07-07 17:34:19,711 - INFO - 下载公司新闻数据: MSFT (2025-01-13)
2025-07-07 17:34:19,779 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-12.json
2025-07-07 17:34:19,780 - INFO - 下载内幕交易数据: AAPL (2025-03-19)
2025-07-07 17:34:20,216 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-13.json
2025-07-07 17:34:20,217 - INFO - 下载公司新闻数据: MSFT (2025-01-14)
2025-07-07 17:34:20,283 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-19.json
2025-07-07 17:34:20,284 - INFO - 下载内幕交易数据: AAPL (2025-03-26)
2025-07-07 17:34:20,721 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-14.json
2025-07-07 17:34:20,722 - INFO - 下载公司新闻数据: MSFT (2025-01-15)
2025-07-07 17:34:20,787 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-26.json
2025-07-07 17:34:20,787 - INFO - 下载内幕交易数据: AAPL (2025-04-02)
2025-07-07 17:34:21,225 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-15.json
2025-07-07 17:34:21,225 - INFO - 下载公司新闻数据: MSFT (2025-01-16)
2025-07-07 17:34:21,291 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-02.json
2025-07-07 17:34:21,291 - INFO - 下载内幕交易数据: AAPL (2025-04-09)
2025-07-07 17:34:21,728 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-16.json
2025-07-07 17:34:21,728 - INFO - 下载公司新闻数据: MSFT (2025-01-17)
2025-07-07 17:34:21,793 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-09.json
2025-07-07 17:34:21,795 - INFO - 下载内幕交易数据: AAPL (2025-04-16)
2025-07-07 17:34:22,230 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-17.json
2025-07-07 17:34:22,230 - INFO - 下载公司新闻数据: MSFT (2025-01-20)
2025-07-07 17:34:22,298 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-16.json
2025-07-07 17:34:22,298 - INFO - 下载内幕交易数据: AAPL (2025-04-23)
2025-07-07 17:34:22,735 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-20.json
2025-07-07 17:34:22,736 - INFO - 下载公司新闻数据: MSFT (2025-01-21)
2025-07-07 17:34:22,801 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-23.json
2025-07-07 17:34:22,802 - INFO - 下载内幕交易数据: AAPL (2025-04-30)
2025-07-07 17:34:23,240 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-21.json
2025-07-07 17:34:23,241 - INFO - 下载公司新闻数据: MSFT (2025-01-22)
2025-07-07 17:34:23,305 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-30.json
2025-07-07 17:34:23,308 - INFO - 下载内幕交易数据: AAPL (2025-05-07)
2025-07-07 17:34:23,745 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-22.json
2025-07-07 17:34:23,746 - INFO - 下载公司新闻数据: MSFT (2025-01-23)
2025-07-07 17:34:23,810 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-07.json
2025-07-07 17:34:23,810 - INFO - 下载内幕交易数据: AAPL (2025-05-14)
2025-07-07 17:34:24,250 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-23.json
2025-07-07 17:34:24,252 - INFO - 下载公司新闻数据: MSFT (2025-01-24)
2025-07-07 17:34:24,314 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-14.json
2025-07-07 17:34:24,314 - INFO - 下载内幕交易数据: AAPL (2025-05-21)
2025-07-07 17:34:24,757 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-24.json
2025-07-07 17:34:24,758 - INFO - 下载公司新闻数据: MSFT (2025-01-27)
2025-07-07 17:34:24,816 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-21.json
2025-07-07 17:34:24,816 - INFO - 下载内幕交易数据: AAPL (2025-05-28)
2025-07-07 17:34:25,262 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-27.json
2025-07-07 17:34:25,263 - INFO - 下载公司新闻数据: MSFT (2025-01-28)
2025-07-07 17:34:25,318 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-28.json
2025-07-07 17:34:25,319 - INFO - 下载内幕交易数据: AAPL (2025-06-01)
2025-07-07 17:34:25,765 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-28.json
2025-07-07 17:34:25,766 - INFO - 下载公司新闻数据: MSFT (2025-01-29)
2025-07-07 17:34:25,820 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-06-01.json
2025-07-07 17:34:25,820 - INFO - AAPL company_news: 生成 109 个采样日期
2025-07-07 17:34:25,822 - INFO - 下载公司新闻数据: AAPL (2025-01-01)
2025-07-07 17:34:26,269 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-29.json
2025-07-07 17:34:26,270 - INFO - 下载公司新闻数据: MSFT (2025-01-30)
2025-07-07 17:34:26,462 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:34:26,772 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-30.json
2025-07-07 17:34:26,773 - INFO - 下载公司新闻数据: MSFT (2025-01-31)
2025-07-07 17:34:27,277 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-31.json
2025-07-07 17:34:27,278 - INFO - 下载公司新闻数据: MSFT (2025-02-03)
2025-07-07 17:34:27,521 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=AAPL&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 17:34:27,780 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-03.json
2025-07-07 17:34:27,783 - INFO - 下载公司新闻数据: MSFT (2025-02-04)
2025-07-07 17:34:27,980 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-01.json
2025-07-07 17:34:27,981 - INFO - 下载公司新闻数据: AAPL (2025-01-02)
2025-07-07 17:34:28,286 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-04.json
2025-07-07 17:34:28,287 - INFO - 下载公司新闻数据: MSFT (2025-02-05)
2025-07-07 17:34:28,484 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-02.json
2025-07-07 17:34:28,485 - INFO - 下载公司新闻数据: AAPL (2025-01-03)
2025-07-07 17:34:28,792 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-05.json
2025-07-07 17:34:28,792 - INFO - 下载公司新闻数据: MSFT (2025-02-06)
2025-07-07 17:34:28,989 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-03.json
2025-07-07 17:34:28,989 - INFO - 下载公司新闻数据: AAPL (2025-01-06)
2025-07-07 17:34:29,296 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-06.json
2025-07-07 17:34:29,296 - INFO - 下载公司新闻数据: MSFT (2025-02-07)
2025-07-07 17:34:29,493 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-06.json
2025-07-07 17:34:29,494 - INFO - 下载公司新闻数据: AAPL (2025-01-07)
2025-07-07 17:34:29,799 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-07.json
2025-07-07 17:34:29,800 - INFO - 下载公司新闻数据: MSFT (2025-02-10)
2025-07-07 17:34:29,998 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-07.json
2025-07-07 17:34:29,999 - INFO - 下载公司新闻数据: AAPL (2025-01-08)
2025-07-07 17:34:30,294 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:34:30,294 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:34:30,304 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-10.json
2025-07-07 17:34:30,305 - INFO - 下载公司新闻数据: MSFT (2025-02-11)
2025-07-07 17:34:30,321 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:34:30,502 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-08.json
2025-07-07 17:34:30,503 - INFO - 下载公司新闻数据: AAPL (2025-01-09)
2025-07-07 17:34:30,812 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-11.json
2025-07-07 17:34:30,813 - INFO - 下载公司新闻数据: MSFT (2025-02-12)
2025-07-07 17:34:31,008 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-09.json
2025-07-07 17:34:31,009 - INFO - 下载公司新闻数据: AAPL (2025-01-10)
2025-07-07 17:34:31,316 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-12.json
2025-07-07 17:34:31,317 - INFO - 下载公司新闻数据: MSFT (2025-02-13)
2025-07-07 17:34:31,513 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-10.json
2025-07-07 17:34:31,513 - INFO - 下载公司新闻数据: AAPL (2025-01-13)
2025-07-07 17:34:31,819 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-13.json
2025-07-07 17:34:31,820 - INFO - 下载公司新闻数据: MSFT (2025-02-14)
2025-07-07 17:34:32,015 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-13.json
2025-07-07 17:34:32,015 - INFO - 下载公司新闻数据: AAPL (2025-01-14)
2025-07-07 17:34:32,324 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-14.json
2025-07-07 17:34:32,324 - INFO - 下载公司新闻数据: MSFT (2025-02-17)
2025-07-07 17:34:32,518 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-14.json
2025-07-07 17:34:32,519 - INFO - 下载公司新闻数据: AAPL (2025-01-15)
2025-07-07 17:34:32,826 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-17.json
2025-07-07 17:34:32,826 - INFO - 下载公司新闻数据: MSFT (2025-02-18)
2025-07-07 17:34:33,021 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-15.json
2025-07-07 17:34:33,023 - INFO - 下载公司新闻数据: AAPL (2025-01-16)
2025-07-07 17:34:33,328 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-18.json
2025-07-07 17:34:33,328 - INFO - 下载公司新闻数据: MSFT (2025-02-19)
2025-07-07 17:34:33,527 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-16.json
2025-07-07 17:34:33,527 - INFO - 下载公司新闻数据: AAPL (2025-01-17)
2025-07-07 17:34:33,832 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-19.json
2025-07-07 17:34:33,833 - INFO - 下载公司新闻数据: MSFT (2025-02-20)
2025-07-07 17:34:34,030 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-17.json
2025-07-07 17:34:34,032 - INFO - 下载公司新闻数据: AAPL (2025-01-20)
2025-07-07 17:34:34,338 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-20.json
2025-07-07 17:34:34,340 - INFO - 下载公司新闻数据: MSFT (2025-02-21)
2025-07-07 17:34:34,536 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-20.json
2025-07-07 17:34:34,537 - INFO - 下载公司新闻数据: AAPL (2025-01-21)
2025-07-07 17:34:34,843 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-21.json
2025-07-07 17:34:34,844 - INFO - 下载公司新闻数据: MSFT (2025-02-24)
2025-07-07 17:34:35,042 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-21.json
2025-07-07 17:34:35,042 - INFO - 下载公司新闻数据: AAPL (2025-01-22)
2025-07-07 17:34:35,346 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-24.json
2025-07-07 17:34:35,347 - INFO - 下载公司新闻数据: MSFT (2025-02-25)
2025-07-07 17:34:35,547 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-22.json
2025-07-07 17:34:35,548 - INFO - 下载公司新闻数据: AAPL (2025-01-23)
2025-07-07 17:34:35,852 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-25.json
2025-07-07 17:34:35,852 - INFO - 下载公司新闻数据: MSFT (2025-02-26)
2025-07-07 17:34:36,049 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-23.json
2025-07-07 17:34:36,050 - INFO - 下载公司新闻数据: AAPL (2025-01-24)
2025-07-07 17:34:36,357 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-26.json
2025-07-07 17:34:36,358 - INFO - 下载公司新闻数据: MSFT (2025-02-27)
2025-07-07 17:34:36,552 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-24.json
2025-07-07 17:34:36,552 - INFO - 下载公司新闻数据: AAPL (2025-01-27)
2025-07-07 17:34:36,859 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-27.json
2025-07-07 17:34:36,860 - INFO - 下载公司新闻数据: MSFT (2025-02-28)
2025-07-07 17:34:37,057 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-27.json
2025-07-07 17:34:37,058 - INFO - 下载公司新闻数据: AAPL (2025-01-28)
2025-07-07 17:34:37,364 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-28.json
2025-07-07 17:34:37,365 - INFO - 下载公司新闻数据: MSFT (2025-03-03)
2025-07-07 17:34:37,560 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-28.json
2025-07-07 17:34:37,562 - INFO - 下载公司新闻数据: AAPL (2025-01-29)
2025-07-07 17:34:37,867 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-03.json
2025-07-07 17:34:37,867 - INFO - 下载公司新闻数据: MSFT (2025-03-04)
2025-07-07 17:34:38,066 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-29.json
2025-07-07 17:34:38,067 - INFO - 下载公司新闻数据: AAPL (2025-01-30)
2025-07-07 17:34:38,372 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-04.json
2025-07-07 17:34:38,373 - INFO - 下载公司新闻数据: MSFT (2025-03-05)
2025-07-07 17:34:38,571 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-30.json
2025-07-07 17:34:38,573 - INFO - 下载公司新闻数据: AAPL (2025-01-31)
2025-07-07 17:34:38,877 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-05.json
2025-07-07 17:34:38,877 - INFO - 下载公司新闻数据: MSFT (2025-03-06)
2025-07-07 17:34:39,075 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-31.json
2025-07-07 17:34:39,076 - INFO - 下载公司新闻数据: AAPL (2025-02-03)
2025-07-07 17:34:39,382 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-06.json
2025-07-07 17:34:39,382 - INFO - 下载公司新闻数据: MSFT (2025-03-07)
2025-07-07 17:34:39,580 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-03.json
2025-07-07 17:34:39,581 - INFO - 下载公司新闻数据: AAPL (2025-02-04)
2025-07-07 17:34:39,884 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-07.json
2025-07-07 17:34:39,884 - INFO - 下载公司新闻数据: MSFT (2025-03-10)
2025-07-07 17:34:40,085 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-04.json
2025-07-07 17:34:40,086 - INFO - 下载公司新闻数据: AAPL (2025-02-05)
2025-07-07 17:34:40,390 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-10.json
2025-07-07 17:34:40,391 - INFO - 下载公司新闻数据: MSFT (2025-03-11)
2025-07-07 17:34:40,591 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-05.json
2025-07-07 17:34:40,592 - INFO - 下载公司新闻数据: AAPL (2025-02-06)
2025-07-07 17:34:40,893 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-11.json
2025-07-07 17:34:40,894 - INFO - 下载公司新闻数据: MSFT (2025-03-12)
2025-07-07 17:34:41,095 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-06.json
2025-07-07 17:34:41,096 - INFO - 下载公司新闻数据: AAPL (2025-02-07)
2025-07-07 17:34:41,395 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-12.json
2025-07-07 17:34:41,395 - INFO - 下载公司新闻数据: MSFT (2025-03-13)
2025-07-07 17:34:41,599 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-07.json
2025-07-07 17:34:41,600 - INFO - 下载公司新闻数据: AAPL (2025-02-10)
2025-07-07 17:34:41,897 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-13.json
2025-07-07 17:34:41,897 - INFO - 下载公司新闻数据: MSFT (2025-03-14)
2025-07-07 17:34:42,102 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-10.json
2025-07-07 17:34:42,102 - INFO - 下载公司新闻数据: AAPL (2025-02-11)
2025-07-07 17:34:42,400 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-14.json
2025-07-07 17:34:42,400 - INFO - 下载公司新闻数据: MSFT (2025-03-17)
2025-07-07 17:34:42,604 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-11.json
2025-07-07 17:34:42,605 - INFO - 下载公司新闻数据: AAPL (2025-02-12)
2025-07-07 17:34:42,904 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-17.json
2025-07-07 17:34:42,905 - INFO - 下载公司新闻数据: MSFT (2025-03-18)
2025-07-07 17:34:43,107 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-12.json
2025-07-07 17:34:43,107 - INFO - 下载公司新闻数据: AAPL (2025-02-13)
2025-07-07 17:34:43,411 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-18.json
2025-07-07 17:34:43,412 - INFO - 下载公司新闻数据: MSFT (2025-03-19)
2025-07-07 17:34:43,610 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-13.json
2025-07-07 17:34:43,611 - INFO - 下载公司新闻数据: AAPL (2025-02-14)
2025-07-07 17:34:43,917 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-19.json
2025-07-07 17:34:43,917 - INFO - 下载公司新闻数据: MSFT (2025-03-20)
2025-07-07 17:34:44,115 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-14.json
2025-07-07 17:34:44,115 - INFO - 下载公司新闻数据: AAPL (2025-02-17)
2025-07-07 17:34:44,423 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-20.json
2025-07-07 17:34:44,424 - INFO - 下载公司新闻数据: MSFT (2025-03-21)
2025-07-07 17:34:44,619 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-17.json
2025-07-07 17:34:44,620 - INFO - 下载公司新闻数据: AAPL (2025-02-18)
2025-07-07 17:34:44,928 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-21.json
2025-07-07 17:34:44,928 - INFO - 下载公司新闻数据: MSFT (2025-03-24)
2025-07-07 17:34:45,125 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-18.json
2025-07-07 17:34:45,126 - INFO - 下载公司新闻数据: AAPL (2025-02-19)
2025-07-07 17:34:45,433 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-24.json
2025-07-07 17:34:45,434 - INFO - 下载公司新闻数据: MSFT (2025-03-25)
2025-07-07 17:34:45,629 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-19.json
2025-07-07 17:34:45,630 - INFO - 下载公司新闻数据: AAPL (2025-02-20)
2025-07-07 17:34:45,937 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-25.json
2025-07-07 17:34:45,938 - INFO - 下载公司新闻数据: MSFT (2025-03-26)
2025-07-07 17:34:46,132 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-20.json
2025-07-07 17:34:46,132 - INFO - 下载公司新闻数据: AAPL (2025-02-21)
2025-07-07 17:34:46,443 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-26.json
2025-07-07 17:34:46,444 - INFO - 下载公司新闻数据: MSFT (2025-03-27)
2025-07-07 17:34:46,637 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-21.json
2025-07-07 17:34:46,637 - INFO - 下载公司新闻数据: AAPL (2025-02-24)
2025-07-07 17:34:46,948 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-27.json
2025-07-07 17:34:46,949 - INFO - 下载公司新闻数据: MSFT (2025-03-28)
2025-07-07 17:34:47,140 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-24.json
2025-07-07 17:34:47,140 - INFO - 下载公司新闻数据: AAPL (2025-02-25)
2025-07-07 17:34:47,453 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-28.json
2025-07-07 17:34:47,454 - INFO - 下载公司新闻数据: MSFT (2025-03-31)
2025-07-07 17:34:47,644 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-25.json
2025-07-07 17:34:47,645 - INFO - 下载公司新闻数据: AAPL (2025-02-26)
2025-07-07 17:34:47,959 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-31.json
2025-07-07 17:34:47,960 - INFO - 下载公司新闻数据: MSFT (2025-04-01)
2025-07-07 17:34:48,150 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-26.json
2025-07-07 17:34:48,150 - INFO - 下载公司新闻数据: AAPL (2025-02-27)
2025-07-07 17:34:48,463 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-01.json
2025-07-07 17:34:48,464 - INFO - 下载公司新闻数据: MSFT (2025-04-02)
2025-07-07 17:34:48,654 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-27.json
2025-07-07 17:34:48,655 - INFO - 下载公司新闻数据: AAPL (2025-02-28)
2025-07-07 17:34:48,967 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-02.json
2025-07-07 17:34:48,969 - INFO - 下载公司新闻数据: MSFT (2025-04-03)
2025-07-07 17:34:49,159 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-28.json
2025-07-07 17:34:49,159 - INFO - 下载公司新闻数据: AAPL (2025-03-03)
2025-07-07 17:34:49,471 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-03.json
2025-07-07 17:34:49,471 - INFO - 下载公司新闻数据: MSFT (2025-04-04)
2025-07-07 17:34:49,663 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-03.json
2025-07-07 17:34:49,664 - INFO - 下载公司新闻数据: AAPL (2025-03-04)
2025-07-07 17:34:49,977 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-04.json
2025-07-07 17:34:49,978 - INFO - 下载公司新闻数据: MSFT (2025-04-07)
2025-07-07 17:34:50,169 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-04.json
2025-07-07 17:34:50,170 - INFO - 下载公司新闻数据: AAPL (2025-03-05)
2025-07-07 17:34:50,484 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-07.json
2025-07-07 17:34:50,485 - INFO - 下载公司新闻数据: MSFT (2025-04-08)
2025-07-07 17:34:50,673 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-05.json
2025-07-07 17:34:50,674 - INFO - 下载公司新闻数据: AAPL (2025-03-06)
2025-07-07 17:34:50,986 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-08.json
2025-07-07 17:34:50,986 - INFO - 下载公司新闻数据: MSFT (2025-04-09)
2025-07-07 17:34:51,176 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-06.json
2025-07-07 17:34:51,176 - INFO - 下载公司新闻数据: AAPL (2025-03-07)
2025-07-07 17:34:51,489 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-09.json
2025-07-07 17:34:51,490 - INFO - 下载公司新闻数据: MSFT (2025-04-10)
2025-07-07 17:34:51,678 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-07.json
2025-07-07 17:34:51,679 - INFO - 下载公司新闻数据: AAPL (2025-03-10)
2025-07-07 17:34:51,994 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-10.json
2025-07-07 17:34:51,995 - INFO - 下载公司新闻数据: MSFT (2025-04-11)
2025-07-07 17:34:52,183 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-10.json
2025-07-07 17:34:52,183 - INFO - 下载公司新闻数据: AAPL (2025-03-11)
2025-07-07 17:34:52,499 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-11.json
2025-07-07 17:34:52,500 - INFO - 下载公司新闻数据: MSFT (2025-04-14)
2025-07-07 17:34:52,688 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-11.json
2025-07-07 17:34:52,689 - INFO - 下载公司新闻数据: AAPL (2025-03-12)
2025-07-07 17:34:53,002 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-14.json
2025-07-07 17:34:53,002 - INFO - 下载公司新闻数据: MSFT (2025-04-15)
2025-07-07 17:34:53,193 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-12.json
2025-07-07 17:34:53,194 - INFO - 下载公司新闻数据: AAPL (2025-03-13)
2025-07-07 17:34:53,504 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-15.json
2025-07-07 17:34:53,505 - INFO - 下载公司新闻数据: MSFT (2025-04-16)
2025-07-07 17:34:53,695 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-13.json
2025-07-07 17:34:53,696 - INFO - 下载公司新闻数据: AAPL (2025-03-14)
2025-07-07 17:34:54,007 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-16.json
2025-07-07 17:34:54,007 - INFO - 下载公司新闻数据: MSFT (2025-04-17)
2025-07-07 17:34:54,199 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-14.json
2025-07-07 17:34:54,199 - INFO - 下载公司新闻数据: AAPL (2025-03-17)
2025-07-07 17:34:54,512 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-17.json
2025-07-07 17:34:54,513 - INFO - 下载公司新闻数据: MSFT (2025-04-18)
2025-07-07 17:34:54,702 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-17.json
2025-07-07 17:34:54,702 - INFO - 下载公司新闻数据: AAPL (2025-03-18)
2025-07-07 17:34:55,018 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-18.json
2025-07-07 17:34:55,018 - INFO - 下载公司新闻数据: MSFT (2025-04-21)
2025-07-07 17:34:55,208 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-18.json
2025-07-07 17:34:55,209 - INFO - 下载公司新闻数据: AAPL (2025-03-19)
2025-07-07 17:34:55,524 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-21.json
2025-07-07 17:34:55,525 - INFO - 下载公司新闻数据: MSFT (2025-04-22)
2025-07-07 17:34:55,712 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-19.json
2025-07-07 17:34:55,712 - INFO - 下载公司新闻数据: AAPL (2025-03-20)
2025-07-07 17:34:56,026 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-22.json
2025-07-07 17:34:56,027 - INFO - 下载公司新闻数据: MSFT (2025-04-23)
2025-07-07 17:34:56,215 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-20.json
2025-07-07 17:34:56,215 - INFO - 下载公司新闻数据: AAPL (2025-03-21)
2025-07-07 17:34:56,530 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-23.json
2025-07-07 17:34:56,530 - INFO - 下载公司新闻数据: MSFT (2025-04-24)
2025-07-07 17:34:56,717 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-21.json
2025-07-07 17:34:56,718 - INFO - 下载公司新闻数据: AAPL (2025-03-24)
2025-07-07 17:34:57,035 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-24.json
2025-07-07 17:34:57,035 - INFO - 下载公司新闻数据: MSFT (2025-04-25)
2025-07-07 17:34:57,220 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-24.json
2025-07-07 17:34:57,220 - INFO - 下载公司新闻数据: AAPL (2025-03-25)
2025-07-07 17:34:57,540 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-25.json
2025-07-07 17:34:57,542 - INFO - 下载公司新闻数据: MSFT (2025-04-28)
2025-07-07 17:34:57,722 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-25.json
2025-07-07 17:34:57,723 - INFO - 下载公司新闻数据: AAPL (2025-03-26)
2025-07-07 17:34:58,045 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-28.json
2025-07-07 17:34:58,046 - INFO - 下载公司新闻数据: MSFT (2025-04-29)
2025-07-07 17:34:58,225 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-26.json
2025-07-07 17:34:58,226 - INFO - 下载公司新闻数据: AAPL (2025-03-27)
2025-07-07 17:34:58,550 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-29.json
2025-07-07 17:34:58,550 - INFO - 下载公司新闻数据: MSFT (2025-04-30)
2025-07-07 17:34:58,728 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-27.json
2025-07-07 17:34:58,729 - INFO - 下载公司新闻数据: AAPL (2025-03-28)
2025-07-07 17:34:59,055 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-30.json
2025-07-07 17:34:59,057 - INFO - 下载公司新闻数据: MSFT (2025-05-01)
2025-07-07 17:34:59,231 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-28.json
2025-07-07 17:34:59,232 - INFO - 下载公司新闻数据: AAPL (2025-03-31)
2025-07-07 17:34:59,560 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-01.json
2025-07-07 17:34:59,560 - INFO - 下载公司新闻数据: MSFT (2025-05-02)
2025-07-07 17:34:59,735 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-31.json
2025-07-07 17:34:59,735 - INFO - 下载公司新闻数据: AAPL (2025-04-01)
2025-07-07 17:35:00,064 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-02.json
2025-07-07 17:35:00,064 - INFO - 下载公司新闻数据: MSFT (2025-05-05)
2025-07-07 17:35:00,239 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-01.json
2025-07-07 17:35:00,240 - INFO - 下载公司新闻数据: AAPL (2025-04-02)
2025-07-07 17:35:00,568 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-05.json
2025-07-07 17:35:00,569 - INFO - 下载公司新闻数据: MSFT (2025-05-06)
2025-07-07 17:35:00,742 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-02.json
2025-07-07 17:35:00,743 - INFO - 下载公司新闻数据: AAPL (2025-04-03)
2025-07-07 17:35:01,074 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-06.json
2025-07-07 17:35:01,075 - INFO - 下载公司新闻数据: MSFT (2025-05-07)
2025-07-07 17:35:01,245 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-03.json
2025-07-07 17:35:01,245 - INFO - 下载公司新闻数据: AAPL (2025-04-04)
2025-07-07 17:35:01,578 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-07.json
2025-07-07 17:35:01,579 - INFO - 下载公司新闻数据: MSFT (2025-05-08)
2025-07-07 17:35:01,747 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-04.json
2025-07-07 17:35:01,747 - INFO - 下载公司新闻数据: AAPL (2025-04-07)
2025-07-07 17:35:02,082 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-08.json
2025-07-07 17:35:02,083 - INFO - 下载公司新闻数据: MSFT (2025-05-09)
2025-07-07 17:35:02,250 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-07.json
2025-07-07 17:35:02,250 - INFO - 下载公司新闻数据: AAPL (2025-04-08)
2025-07-07 17:35:02,322 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 17:35:02,325 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:35:02,588 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-09.json
2025-07-07 17:35:02,590 - INFO - 下载公司新闻数据: MSFT (2025-05-12)
2025-07-07 17:35:02,752 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-08.json
2025-07-07 17:35:02,752 - INFO - 下载公司新闻数据: AAPL (2025-04-09)
2025-07-07 17:35:03,092 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-12.json
2025-07-07 17:35:03,092 - INFO - 下载公司新闻数据: MSFT (2025-05-13)
2025-07-07 17:35:03,254 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-09.json
2025-07-07 17:35:03,255 - INFO - 下载公司新闻数据: AAPL (2025-04-10)
2025-07-07 17:35:03,330 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50 HTTP/1.1" 200 None
2025-07-07 17:35:03,546 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-01.json
2025-07-07 17:35:03,547 - INFO - 下载内幕交易数据: NVDA (2025-01-08)
2025-07-07 17:35:03,594 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-13.json
2025-07-07 17:35:03,595 - INFO - 下载公司新闻数据: MSFT (2025-05-14)
2025-07-07 17:35:03,757 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-10.json
2025-07-07 17:35:03,758 - INFO - 下载公司新闻数据: AAPL (2025-04-11)
2025-07-07 17:35:04,050 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-08.json
2025-07-07 17:35:04,050 - INFO - 下载内幕交易数据: NVDA (2025-01-15)
2025-07-07 17:35:04,098 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-14.json
2025-07-07 17:35:04,099 - INFO - 下载公司新闻数据: MSFT (2025-05-15)
2025-07-07 17:35:04,262 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-11.json
2025-07-07 17:35:04,263 - INFO - 下载公司新闻数据: AAPL (2025-04-14)
2025-07-07 17:35:04,555 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-15.json
2025-07-07 17:35:04,556 - INFO - 下载内幕交易数据: NVDA (2025-01-22)
2025-07-07 17:35:04,604 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-15.json
2025-07-07 17:35:04,606 - INFO - 下载公司新闻数据: MSFT (2025-05-16)
2025-07-07 17:35:04,765 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-14.json
2025-07-07 17:35:04,765 - INFO - 下载公司新闻数据: AAPL (2025-04-15)
2025-07-07 17:35:05,058 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-22.json
2025-07-07 17:35:05,058 - INFO - 下载内幕交易数据: NVDA (2025-01-29)
2025-07-07 17:35:05,110 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-16.json
2025-07-07 17:35:05,110 - INFO - 下载公司新闻数据: MSFT (2025-05-19)
2025-07-07 17:35:05,268 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-15.json
2025-07-07 17:35:05,268 - INFO - 下载公司新闻数据: AAPL (2025-04-16)
2025-07-07 17:35:05,562 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-29.json
2025-07-07 17:35:05,563 - INFO - 下载内幕交易数据: NVDA (2025-02-05)
2025-07-07 17:35:05,614 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-19.json
2025-07-07 17:35:05,614 - INFO - 下载公司新闻数据: MSFT (2025-05-20)
2025-07-07 17:35:05,769 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-16.json
2025-07-07 17:35:05,770 - INFO - 下载公司新闻数据: AAPL (2025-04-17)
2025-07-07 17:35:06,065 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-05.json
2025-07-07 17:35:06,065 - INFO - 下载内幕交易数据: NVDA (2025-02-12)
2025-07-07 17:35:06,116 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-20.json
2025-07-07 17:35:06,116 - INFO - 下载公司新闻数据: MSFT (2025-05-21)
2025-07-07 17:35:06,274 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-17.json
2025-07-07 17:35:06,275 - INFO - 下载公司新闻数据: AAPL (2025-04-18)
2025-07-07 17:35:06,566 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-12.json
2025-07-07 17:35:06,568 - INFO - 下载内幕交易数据: NVDA (2025-02-19)
2025-07-07 17:35:06,619 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-21.json
2025-07-07 17:35:06,619 - INFO - 下载公司新闻数据: MSFT (2025-05-22)
2025-07-07 17:35:06,777 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-18.json
2025-07-07 17:35:06,777 - INFO - 下载公司新闻数据: AAPL (2025-04-21)
2025-07-07 17:35:07,070 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-19.json
2025-07-07 17:35:07,072 - INFO - 下载内幕交易数据: NVDA (2025-02-26)
2025-07-07 17:35:07,124 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-22.json
2025-07-07 17:35:07,125 - INFO - 下载公司新闻数据: MSFT (2025-05-23)
2025-07-07 17:35:07,279 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-21.json
2025-07-07 17:35:07,279 - INFO - 下载公司新闻数据: AAPL (2025-04-22)
2025-07-07 17:35:07,575 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-26.json
2025-07-07 17:35:07,576 - INFO - 下载内幕交易数据: NVDA (2025-03-05)
2025-07-07 17:35:07,628 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-23.json
2025-07-07 17:35:07,629 - INFO - 下载公司新闻数据: MSFT (2025-05-26)
2025-07-07 17:35:07,782 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-22.json
2025-07-07 17:35:07,783 - INFO - 下载公司新闻数据: AAPL (2025-04-23)
2025-07-07 17:35:08,080 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-05.json
2025-07-07 17:35:08,081 - INFO - 下载内幕交易数据: NVDA (2025-03-12)
2025-07-07 17:35:08,132 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-26.json
2025-07-07 17:35:08,132 - INFO - 下载公司新闻数据: MSFT (2025-05-27)
2025-07-07 17:35:08,286 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-23.json
2025-07-07 17:35:08,287 - INFO - 下载公司新闻数据: AAPL (2025-04-24)
2025-07-07 17:35:08,583 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-12.json
2025-07-07 17:35:08,584 - INFO - 下载内幕交易数据: NVDA (2025-03-19)
2025-07-07 17:35:08,636 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-27.json
2025-07-07 17:35:08,636 - INFO - 下载公司新闻数据: MSFT (2025-05-28)
2025-07-07 17:35:08,788 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-24.json
2025-07-07 17:35:08,789 - INFO - 下载公司新闻数据: AAPL (2025-04-25)
2025-07-07 17:35:09,086 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-19.json
2025-07-07 17:35:09,087 - INFO - 下载内幕交易数据: NVDA (2025-03-26)
2025-07-07 17:35:09,141 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-28.json
2025-07-07 17:35:09,143 - INFO - 下载公司新闻数据: MSFT (2025-05-29)
2025-07-07 17:35:09,293 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-25.json
2025-07-07 17:35:09,293 - INFO - 下载公司新闻数据: AAPL (2025-04-28)
2025-07-07 17:35:09,592 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-26.json
2025-07-07 17:35:09,593 - INFO - 下载内幕交易数据: NVDA (2025-04-02)
2025-07-07 17:35:09,648 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-29.json
2025-07-07 17:35:09,648 - INFO - 下载公司新闻数据: MSFT (2025-05-30)
2025-07-07 17:35:09,797 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-28.json
2025-07-07 17:35:09,797 - INFO - 下载公司新闻数据: AAPL (2025-04-29)
2025-07-07 17:35:10,095 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-02.json
2025-07-07 17:35:10,096 - INFO - 下载内幕交易数据: NVDA (2025-04-09)
2025-07-07 17:35:10,151 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-30.json
2025-07-07 17:35:10,151 - INFO - 下载公司新闻数据: MSFT (2025-06-01)
2025-07-07 17:35:10,300 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-29.json
2025-07-07 17:35:10,301 - INFO - 下载公司新闻数据: AAPL (2025-04-30)
2025-07-07 17:35:10,600 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-09.json
2025-07-07 17:35:10,601 - INFO - 下载内幕交易数据: NVDA (2025-04-16)
2025-07-07 17:35:10,655 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-06-01.json
2025-07-07 17:35:10,657 - INFO - MSFT market_cap: 生成 109 个采样日期
2025-07-07 17:35:10,659 - INFO - 下载市值数据: MSFT (2025-01-01)
2025-07-07 17:35:10,804 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-30.json
2025-07-07 17:35:10,805 - INFO - 下载公司新闻数据: AAPL (2025-05-01)
2025-07-07 17:35:11,102 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-16.json
2025-07-07 17:35:11,103 - INFO - 下载内幕交易数据: NVDA (2025-04-23)
2025-07-07 17:35:11,160 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-01.json
2025-07-07 17:35:11,161 - INFO - 下载市值数据: MSFT (2025-01-02)
2025-07-07 17:35:11,308 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-01.json
2025-07-07 17:35:11,308 - INFO - 下载公司新闻数据: AAPL (2025-05-02)
2025-07-07 17:35:11,606 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-23.json
2025-07-07 17:35:11,607 - INFO - 下载内幕交易数据: NVDA (2025-04-30)
2025-07-07 17:35:11,663 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-02.json
2025-07-07 17:35:11,663 - INFO - 下载市值数据: MSFT (2025-01-03)
2025-07-07 17:35:11,812 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-02.json
2025-07-07 17:35:11,812 - INFO - 下载公司新闻数据: AAPL (2025-05-05)
2025-07-07 17:35:12,108 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-30.json
2025-07-07 17:35:12,109 - INFO - 下载内幕交易数据: NVDA (2025-05-07)
2025-07-07 17:35:12,164 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-03.json
2025-07-07 17:35:12,164 - INFO - 下载市值数据: MSFT (2025-01-06)
2025-07-07 17:35:12,316 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-05.json
2025-07-07 17:35:12,317 - INFO - 下载公司新闻数据: AAPL (2025-05-06)
2025-07-07 17:35:12,612 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-07.json
2025-07-07 17:35:12,612 - INFO - 下载内幕交易数据: NVDA (2025-05-14)
2025-07-07 17:35:12,665 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-06.json
2025-07-07 17:35:12,666 - INFO - 下载市值数据: MSFT (2025-01-07)
2025-07-07 17:35:12,819 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-06.json
2025-07-07 17:35:12,819 - INFO - 下载公司新闻数据: AAPL (2025-05-07)
2025-07-07 17:35:13,115 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-14.json
2025-07-07 17:35:13,116 - INFO - 下载内幕交易数据: NVDA (2025-05-21)
2025-07-07 17:35:13,167 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-07.json
2025-07-07 17:35:13,167 - INFO - 下载市值数据: MSFT (2025-01-08)
2025-07-07 17:35:13,322 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-07.json
2025-07-07 17:35:13,323 - INFO - 下载公司新闻数据: AAPL (2025-05-08)
2025-07-07 17:35:13,619 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-21.json
2025-07-07 17:35:13,619 - INFO - 下载内幕交易数据: NVDA (2025-05-28)
2025-07-07 17:35:13,670 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-08.json
2025-07-07 17:35:13,670 - INFO - 下载市值数据: MSFT (2025-01-09)
2025-07-07 17:35:13,824 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-08.json
2025-07-07 17:35:13,825 - INFO - 下载公司新闻数据: AAPL (2025-05-09)
2025-07-07 17:35:14,124 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-28.json
2025-07-07 17:35:14,125 - INFO - 下载内幕交易数据: NVDA (2025-06-01)
2025-07-07 17:35:14,172 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-09.json
2025-07-07 17:35:14,173 - INFO - 下载市值数据: MSFT (2025-01-10)
2025-07-07 17:35:14,326 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-09.json
2025-07-07 17:35:14,327 - INFO - 下载公司新闻数据: AAPL (2025-05-12)
2025-07-07 17:35:14,628 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-06-01.json
2025-07-07 17:35:14,629 - INFO - NVDA company_news: 生成 109 个采样日期
2025-07-07 17:35:14,629 - INFO - 下载公司新闻数据: NVDA (2025-01-01)
2025-07-07 17:35:14,676 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-10.json
2025-07-07 17:35:14,676 - INFO - 下载市值数据: MSFT (2025-01-13)
2025-07-07 17:35:14,830 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-12.json
2025-07-07 17:35:14,830 - INFO - 下载公司新闻数据: AAPL (2025-05-13)
2025-07-07 17:35:15,177 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-13.json
2025-07-07 17:35:15,177 - INFO - 下载市值数据: MSFT (2025-01-14)
2025-07-07 17:35:15,279 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:35:15,332 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-13.json
2025-07-07 17:35:15,332 - INFO - 下载公司新闻数据: AAPL (2025-05-14)
2025-07-07 17:35:15,367 - DEBUG - Incremented Retry for (url='/news/?ticker=NVDA&end_date=2025-01-01&limit=100'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:35:15,367 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 17:35:15,367 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:35:15,447 - DEBUG - Incremented Retry for (url='/news/?ticker=NVDA&end_date=2025-01-01&limit=100'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:35:15,678 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-14.json
2025-07-07 17:35:15,679 - INFO - 下载市值数据: MSFT (2025-01-15)
2025-07-07 17:35:15,835 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-14.json
2025-07-07 17:35:15,835 - INFO - 下载公司新闻数据: AAPL (2025-05-15)
2025-07-07 17:35:16,180 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-15.json
2025-07-07 17:35:16,180 - INFO - 下载市值数据: MSFT (2025-01-16)
2025-07-07 17:35:16,339 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-15.json
2025-07-07 17:35:16,341 - INFO - 下载公司新闻数据: AAPL (2025-05-16)
2025-07-07 17:35:16,682 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-16.json
2025-07-07 17:35:16,683 - INFO - 下载市值数据: MSFT (2025-01-17)
2025-07-07 17:35:16,844 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-16.json
2025-07-07 17:35:16,845 - INFO - 下载公司新闻数据: AAPL (2025-05-19)
2025-07-07 17:35:17,184 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-17.json
2025-07-07 17:35:17,185 - INFO - 下载市值数据: MSFT (2025-01-20)
2025-07-07 17:35:17,347 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-19.json
2025-07-07 17:35:17,347 - INFO - 下载公司新闻数据: AAPL (2025-05-20)
2025-07-07 17:35:17,685 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-20.json
2025-07-07 17:35:17,685 - INFO - 下载市值数据: MSFT (2025-01-21)
2025-07-07 17:35:17,851 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-20.json
2025-07-07 17:35:17,852 - INFO - 下载公司新闻数据: AAPL (2025-05-21)
2025-07-07 17:35:18,187 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-21.json
2025-07-07 17:35:18,187 - INFO - 下载市值数据: MSFT (2025-01-22)
2025-07-07 17:35:18,356 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-21.json
2025-07-07 17:35:18,358 - INFO - 下载公司新闻数据: AAPL (2025-05-22)
2025-07-07 17:35:18,689 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-22.json
2025-07-07 17:35:18,691 - INFO - 下载市值数据: MSFT (2025-01-23)
2025-07-07 17:35:18,860 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-22.json
2025-07-07 17:35:18,862 - INFO - 下载公司新闻数据: AAPL (2025-05-23)
2025-07-07 17:35:19,193 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-23.json
2025-07-07 17:35:19,194 - INFO - 下载市值数据: MSFT (2025-01-24)
2025-07-07 17:35:19,365 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-23.json
2025-07-07 17:35:19,365 - INFO - 下载公司新闻数据: AAPL (2025-05-26)
2025-07-07 17:35:19,448 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 17:35:19,449 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:35:19,517 - DEBUG - Incremented Retry for (url='/news/?ticker=NVDA&end_date=2025-01-01&limit=100'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:35:19,696 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-24.json
2025-07-07 17:35:19,697 - INFO - 下载市值数据: MSFT (2025-01-27)
2025-07-07 17:35:19,867 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-26.json
2025-07-07 17:35:19,867 - INFO - 下载公司新闻数据: AAPL (2025-05-27)
2025-07-07 17:35:20,199 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-27.json
2025-07-07 17:35:20,200 - INFO - 下载市值数据: MSFT (2025-01-28)
2025-07-07 17:35:20,370 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-27.json
2025-07-07 17:35:20,371 - INFO - 下载公司新闻数据: AAPL (2025-05-28)
2025-07-07 17:35:20,702 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-28.json
2025-07-07 17:35:20,702 - INFO - 下载市值数据: MSFT (2025-01-29)
2025-07-07 17:35:20,875 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-28.json
2025-07-07 17:35:20,876 - INFO - 下载公司新闻数据: AAPL (2025-05-29)
2025-07-07 17:35:21,204 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-29.json
2025-07-07 17:35:21,204 - INFO - 下载市值数据: MSFT (2025-01-30)
2025-07-07 17:35:21,378 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-29.json
2025-07-07 17:35:21,379 - INFO - 下载公司新闻数据: AAPL (2025-05-30)
2025-07-07 17:35:21,705 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-30.json
2025-07-07 17:35:21,706 - INFO - 下载市值数据: MSFT (2025-01-31)
2025-07-07 17:35:21,881 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-30.json
2025-07-07 17:35:21,881 - INFO - 下载公司新闻数据: AAPL (2025-06-01)
2025-07-07 17:35:22,208 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-31.json
2025-07-07 17:35:22,209 - INFO - 下载市值数据: MSFT (2025-02-03)
2025-07-07 17:35:22,384 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-06-01.json
2025-07-07 17:35:22,386 - INFO - AAPL market_cap: 生成 109 个采样日期
2025-07-07 17:35:22,386 - INFO - 下载市值数据: AAPL (2025-01-01)
2025-07-07 17:35:22,710 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-03.json
2025-07-07 17:35:22,712 - INFO - 下载市值数据: MSFT (2025-02-04)
2025-07-07 17:35:22,889 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-01.json
2025-07-07 17:35:22,889 - INFO - 下载市值数据: AAPL (2025-01-02)
2025-07-07 17:35:23,214 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-04.json
2025-07-07 17:35:23,214 - INFO - 下载市值数据: MSFT (2025-02-05)
2025-07-07 17:35:23,390 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-02.json
2025-07-07 17:35:23,390 - INFO - 下载市值数据: AAPL (2025-01-03)
2025-07-07 17:35:23,717 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-05.json
2025-07-07 17:35:23,717 - INFO - 下载市值数据: MSFT (2025-02-06)
2025-07-07 17:35:23,892 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-03.json
2025-07-07 17:35:23,892 - INFO - 下载市值数据: AAPL (2025-01-06)
2025-07-07 17:35:24,219 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-06.json
2025-07-07 17:35:24,219 - INFO - 下载市值数据: MSFT (2025-02-07)
2025-07-07 17:35:24,394 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-06.json
2025-07-07 17:35:24,395 - INFO - 下载市值数据: AAPL (2025-01-07)
2025-07-07 17:35:24,721 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-07.json
2025-07-07 17:35:24,723 - INFO - 下载市值数据: MSFT (2025-02-10)
2025-07-07 17:35:24,896 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-07.json
2025-07-07 17:35:24,896 - INFO - 下载市值数据: AAPL (2025-01-08)
2025-07-07 17:35:25,225 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-10.json
2025-07-07 17:35:25,226 - INFO - 下载市值数据: MSFT (2025-02-11)
2025-07-07 17:35:25,397 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-08.json
2025-07-07 17:35:25,398 - INFO - 下载市值数据: AAPL (2025-01-09)
2025-07-07 17:35:25,728 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-11.json
2025-07-07 17:35:25,729 - INFO - 下载市值数据: MSFT (2025-02-12)
2025-07-07 17:35:25,899 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-09.json
2025-07-07 17:35:25,899 - INFO - 下载市值数据: AAPL (2025-01-10)
2025-07-07 17:35:26,231 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-12.json
2025-07-07 17:35:26,231 - INFO - 下载市值数据: MSFT (2025-02-13)
2025-07-07 17:35:26,400 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-10.json
2025-07-07 17:35:26,401 - INFO - 下载市值数据: AAPL (2025-01-13)
2025-07-07 17:35:26,732 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-13.json
2025-07-07 17:35:26,733 - INFO - 下载市值数据: MSFT (2025-02-14)
2025-07-07 17:35:26,903 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-13.json
2025-07-07 17:35:26,904 - INFO - 下载市值数据: AAPL (2025-01-14)
2025-07-07 17:35:27,236 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-14.json
2025-07-07 17:35:27,237 - INFO - 下载市值数据: MSFT (2025-02-17)
2025-07-07 17:35:27,405 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-14.json
2025-07-07 17:35:27,406 - INFO - 下载市值数据: AAPL (2025-01-15)
2025-07-07 17:35:27,518 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 17:35:27,518 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:35:27,614 - DEBUG - Incremented Retry for (url='/news/?ticker=NVDA&end_date=2025-01-01&limit=100'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:35:27,739 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-17.json
2025-07-07 17:35:27,740 - INFO - 下载市值数据: MSFT (2025-02-18)
2025-07-07 17:35:27,907 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-15.json
2025-07-07 17:35:27,908 - INFO - 下载市值数据: AAPL (2025-01-16)
2025-07-07 17:35:28,242 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-18.json
2025-07-07 17:35:28,243 - INFO - 下载市值数据: MSFT (2025-02-19)
2025-07-07 17:35:28,409 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-16.json
2025-07-07 17:35:28,409 - INFO - 下载市值数据: AAPL (2025-01-17)
2025-07-07 17:35:28,744 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-19.json
2025-07-07 17:35:28,745 - INFO - 下载市值数据: MSFT (2025-02-20)
2025-07-07 17:35:28,910 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-17.json
2025-07-07 17:35:28,910 - INFO - 下载市值数据: AAPL (2025-01-20)
2025-07-07 17:35:29,247 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-20.json
2025-07-07 17:35:29,247 - INFO - 下载市值数据: MSFT (2025-02-21)
2025-07-07 17:35:29,412 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-20.json
2025-07-07 17:35:29,412 - INFO - 下载市值数据: AAPL (2025-01-21)
2025-07-07 17:35:29,749 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-21.json
2025-07-07 17:35:29,750 - INFO - 下载市值数据: MSFT (2025-02-24)
2025-07-07 17:35:29,914 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-21.json
2025-07-07 17:35:29,915 - INFO - 下载市值数据: AAPL (2025-01-22)
2025-07-07 17:35:30,252 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-24.json
2025-07-07 17:35:30,252 - INFO - 下载市值数据: MSFT (2025-02-25)
2025-07-07 17:35:30,416 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-22.json
2025-07-07 17:35:30,416 - INFO - 下载市值数据: AAPL (2025-01-23)
2025-07-07 17:35:30,754 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-25.json
2025-07-07 17:35:30,756 - INFO - 下载市值数据: MSFT (2025-02-26)
2025-07-07 17:35:30,918 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-23.json
2025-07-07 17:35:30,918 - INFO - 下载市值数据: AAPL (2025-01-24)
2025-07-07 17:35:31,258 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-26.json
2025-07-07 17:35:31,259 - INFO - 下载市值数据: MSFT (2025-02-27)
2025-07-07 17:35:31,419 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-24.json
2025-07-07 17:35:31,420 - INFO - 下载市值数据: AAPL (2025-01-27)
2025-07-07 17:35:31,762 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-27.json
2025-07-07 17:35:31,762 - INFO - 下载市值数据: MSFT (2025-02-28)
2025-07-07 17:35:31,920 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-27.json
2025-07-07 17:35:31,920 - INFO - 下载市值数据: AAPL (2025-01-28)
2025-07-07 17:35:32,264 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-28.json
2025-07-07 17:35:32,265 - INFO - 下载市值数据: MSFT (2025-03-03)
2025-07-07 17:35:32,422 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-28.json
2025-07-07 17:35:32,424 - INFO - 下载市值数据: AAPL (2025-01-29)
2025-07-07 17:35:32,767 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-03.json
2025-07-07 17:35:32,768 - INFO - 下载市值数据: MSFT (2025-03-04)
2025-07-07 17:35:32,925 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-29.json
2025-07-07 17:35:32,926 - INFO - 下载市值数据: AAPL (2025-01-30)
2025-07-07 17:35:33,270 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-04.json
2025-07-07 17:35:33,270 - INFO - 下载市值数据: MSFT (2025-03-05)
2025-07-07 17:35:33,427 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-30.json
2025-07-07 17:35:33,429 - INFO - 下载市值数据: AAPL (2025-01-31)
2025-07-07 17:35:33,772 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-05.json
2025-07-07 17:35:33,773 - INFO - 下载市值数据: MSFT (2025-03-06)
2025-07-07 17:35:33,930 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-31.json
2025-07-07 17:35:33,930 - INFO - 下载市值数据: AAPL (2025-02-03)
2025-07-07 17:35:34,275 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-06.json
2025-07-07 17:35:34,275 - INFO - 下载市值数据: MSFT (2025-03-07)
2025-07-07 17:35:34,432 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-03.json
2025-07-07 17:35:34,432 - INFO - 下载市值数据: AAPL (2025-02-04)
2025-07-07 17:35:34,776 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-07.json
2025-07-07 17:35:34,777 - INFO - 下载市值数据: MSFT (2025-03-10)
2025-07-07 17:35:34,934 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-04.json
2025-07-07 17:35:34,935 - INFO - 下载市值数据: AAPL (2025-02-05)
2025-07-07 17:35:35,279 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-10.json
2025-07-07 17:35:35,279 - INFO - 下载市值数据: MSFT (2025-03-11)
2025-07-07 17:35:35,437 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-05.json
2025-07-07 17:35:35,440 - INFO - 下载市值数据: AAPL (2025-02-06)
2025-07-07 17:35:35,782 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-11.json
2025-07-07 17:35:35,783 - INFO - 下载市值数据: MSFT (2025-03-12)
2025-07-07 17:35:35,943 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-06.json
2025-07-07 17:35:35,943 - INFO - 下载市值数据: AAPL (2025-02-07)
2025-07-07 17:35:36,284 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-12.json
2025-07-07 17:35:36,285 - INFO - 下载市值数据: MSFT (2025-03-13)
2025-07-07 17:35:36,445 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-07.json
2025-07-07 17:35:36,445 - INFO - 下载市值数据: AAPL (2025-02-10)
2025-07-07 17:35:36,786 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-13.json
2025-07-07 17:35:36,786 - INFO - 下载市值数据: MSFT (2025-03-14)
2025-07-07 17:35:36,947 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-10.json
2025-07-07 17:35:36,948 - INFO - 下载市值数据: AAPL (2025-02-11)
2025-07-07 17:35:37,288 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-14.json
2025-07-07 17:35:37,289 - INFO - 下载市值数据: MSFT (2025-03-17)
2025-07-07 17:35:37,449 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-11.json
2025-07-07 17:35:37,449 - INFO - 下载市值数据: AAPL (2025-02-12)
2025-07-07 17:35:37,792 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-17.json
2025-07-07 17:35:37,792 - INFO - 下载市值数据: MSFT (2025-03-18)
2025-07-07 17:35:37,950 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-12.json
2025-07-07 17:35:37,951 - INFO - 下载市值数据: AAPL (2025-02-13)
2025-07-07 17:35:38,293 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-18.json
2025-07-07 17:35:38,295 - INFO - 下载市值数据: MSFT (2025-03-19)
2025-07-07 17:35:38,452 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-13.json
2025-07-07 17:35:38,453 - INFO - 下载市值数据: AAPL (2025-02-14)
2025-07-07 17:35:38,798 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-19.json
2025-07-07 17:35:38,799 - INFO - 下载市值数据: MSFT (2025-03-20)
2025-07-07 17:35:38,955 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-14.json
2025-07-07 17:35:38,956 - INFO - 下载市值数据: AAPL (2025-02-17)
2025-07-07 17:35:39,300 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-20.json
2025-07-07 17:35:39,300 - INFO - 下载市值数据: MSFT (2025-03-21)
2025-07-07 17:35:39,458 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-17.json
2025-07-07 17:35:39,459 - INFO - 下载市值数据: AAPL (2025-02-18)
2025-07-07 17:35:39,803 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-21.json
2025-07-07 17:35:39,804 - INFO - 下载市值数据: MSFT (2025-03-24)
2025-07-07 17:35:39,960 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-18.json
2025-07-07 17:35:39,961 - INFO - 下载市值数据: AAPL (2025-02-19)
2025-07-07 17:35:40,306 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-24.json
2025-07-07 17:35:40,307 - INFO - 下载市值数据: MSFT (2025-03-25)
2025-07-07 17:35:40,463 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-19.json
2025-07-07 17:35:40,463 - INFO - 下载市值数据: AAPL (2025-02-20)
2025-07-07 17:35:40,809 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-25.json
2025-07-07 17:35:40,809 - INFO - 下载市值数据: MSFT (2025-03-26)
2025-07-07 17:35:40,964 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-20.json
2025-07-07 17:35:40,965 - INFO - 下载市值数据: AAPL (2025-02-21)
2025-07-07 17:35:41,312 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-26.json
2025-07-07 17:35:41,313 - INFO - 下载市值数据: MSFT (2025-03-27)
2025-07-07 17:35:41,465 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-21.json
2025-07-07 17:35:41,466 - INFO - 下载市值数据: AAPL (2025-02-24)
2025-07-07 17:35:41,815 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-27.json
2025-07-07 17:35:41,816 - INFO - 下载市值数据: MSFT (2025-03-28)
2025-07-07 17:35:41,968 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-24.json
2025-07-07 17:35:41,969 - INFO - 下载市值数据: AAPL (2025-02-25)
2025-07-07 17:35:42,316 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-28.json
2025-07-07 17:35:42,317 - INFO - 下载市值数据: MSFT (2025-03-31)
2025-07-07 17:35:42,470 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-25.json
2025-07-07 17:35:42,472 - INFO - 下载市值数据: AAPL (2025-02-26)
2025-07-07 17:35:42,819 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-31.json
2025-07-07 17:35:42,820 - INFO - 下载市值数据: MSFT (2025-04-01)
2025-07-07 17:35:42,975 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-26.json
2025-07-07 17:35:42,976 - INFO - 下载市值数据: AAPL (2025-02-27)
2025-07-07 17:35:43,323 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-01.json
2025-07-07 17:35:43,325 - INFO - 下载市值数据: MSFT (2025-04-02)
2025-07-07 17:35:43,478 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-27.json
2025-07-07 17:35:43,478 - INFO - 下载市值数据: AAPL (2025-02-28)
2025-07-07 17:35:43,615 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 17:35:43,616 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:35:43,637 - DEBUG - Incremented Retry for (url='/news/?ticker=NVDA&end_date=2025-01-01&limit=100'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:35:43,827 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-02.json
2025-07-07 17:35:43,828 - INFO - 下载市值数据: MSFT (2025-04-03)
2025-07-07 17:35:43,980 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-28.json
2025-07-07 17:35:43,981 - INFO - 下载市值数据: AAPL (2025-03-03)
2025-07-07 17:35:44,330 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-03.json
2025-07-07 17:35:44,331 - INFO - 下载市值数据: MSFT (2025-04-04)
2025-07-07 17:35:44,482 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-03.json
2025-07-07 17:35:44,482 - INFO - 下载市值数据: AAPL (2025-03-04)
2025-07-07 17:35:44,832 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-04.json
2025-07-07 17:35:44,832 - INFO - 下载市值数据: MSFT (2025-04-07)
2025-07-07 17:35:44,983 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-04.json
2025-07-07 17:35:44,983 - INFO - 下载市值数据: AAPL (2025-03-05)
2025-07-07 17:35:45,333 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-07.json
2025-07-07 17:35:45,334 - INFO - 下载市值数据: MSFT (2025-04-08)
2025-07-07 17:35:45,485 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-05.json
2025-07-07 17:35:45,486 - INFO - 下载市值数据: AAPL (2025-03-06)
2025-07-07 17:35:45,837 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-08.json
2025-07-07 17:35:45,837 - INFO - 下载市值数据: MSFT (2025-04-09)
2025-07-07 17:35:45,987 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-06.json
2025-07-07 17:35:45,989 - INFO - 下载市值数据: AAPL (2025-03-07)
2025-07-07 17:35:46,339 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-09.json
2025-07-07 17:35:46,340 - INFO - 下载市值数据: MSFT (2025-04-10)
2025-07-07 17:35:46,490 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-07.json
2025-07-07 17:35:46,490 - INFO - 下载市值数据: AAPL (2025-03-10)
2025-07-07 17:35:46,842 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-10.json
2025-07-07 17:35:46,843 - INFO - 下载市值数据: MSFT (2025-04-11)
2025-07-07 17:35:46,993 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-10.json
2025-07-07 17:35:46,993 - INFO - 下载市值数据: AAPL (2025-03-11)
2025-07-07 17:35:47,345 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-11.json
2025-07-07 17:35:47,346 - INFO - 下载市值数据: MSFT (2025-04-14)
2025-07-07 17:35:47,495 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-11.json
2025-07-07 17:35:47,496 - INFO - 下载市值数据: AAPL (2025-03-12)
2025-07-07 17:35:47,848 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-14.json
2025-07-07 17:35:47,848 - INFO - 下载市值数据: MSFT (2025-04-15)
2025-07-07 17:35:47,997 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-12.json
2025-07-07 17:35:47,997 - INFO - 下载市值数据: AAPL (2025-03-13)
2025-07-07 17:35:48,350 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-15.json
2025-07-07 17:35:48,350 - INFO - 下载市值数据: MSFT (2025-04-16)
2025-07-07 17:35:48,499 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-13.json
2025-07-07 17:35:48,499 - INFO - 下载市值数据: AAPL (2025-03-14)
2025-07-07 17:35:48,852 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-16.json
2025-07-07 17:35:48,853 - INFO - 下载市值数据: MSFT (2025-04-17)
2025-07-07 17:35:49,000 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-14.json
2025-07-07 17:35:49,001 - INFO - 下载市值数据: AAPL (2025-03-17)
2025-07-07 17:35:49,354 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-17.json
2025-07-07 17:35:49,355 - INFO - 下载市值数据: MSFT (2025-04-18)
2025-07-07 17:35:49,503 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-17.json
2025-07-07 17:35:49,504 - INFO - 下载市值数据: AAPL (2025-03-18)
2025-07-07 17:35:49,858 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-18.json
2025-07-07 17:35:49,858 - INFO - 下载市值数据: MSFT (2025-04-21)
2025-07-07 17:35:50,005 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-18.json
2025-07-07 17:35:50,006 - INFO - 下载市值数据: AAPL (2025-03-19)
2025-07-07 17:35:50,360 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-21.json
2025-07-07 17:35:50,361 - INFO - 下载市值数据: MSFT (2025-04-22)
2025-07-07 17:35:50,509 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-19.json
2025-07-07 17:35:50,510 - INFO - 下载市值数据: AAPL (2025-03-20)
2025-07-07 17:35:50,861 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-22.json
2025-07-07 17:35:50,862 - INFO - 下载市值数据: MSFT (2025-04-23)
2025-07-07 17:35:51,010 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-20.json
2025-07-07 17:35:51,010 - INFO - 下载市值数据: AAPL (2025-03-21)
2025-07-07 17:35:51,363 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-23.json
2025-07-07 17:35:51,364 - INFO - 下载市值数据: MSFT (2025-04-24)
2025-07-07 17:35:51,512 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-21.json
2025-07-07 17:35:51,513 - INFO - 下载市值数据: AAPL (2025-03-24)
2025-07-07 17:35:51,864 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-24.json
2025-07-07 17:35:51,864 - INFO - 下载市值数据: MSFT (2025-04-25)
2025-07-07 17:35:52,014 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-24.json
2025-07-07 17:35:52,014 - INFO - 下载市值数据: AAPL (2025-03-25)
2025-07-07 17:35:52,366 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-25.json
2025-07-07 17:35:52,367 - INFO - 下载市值数据: MSFT (2025-04-28)
2025-07-07 17:35:52,516 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-25.json
2025-07-07 17:35:52,516 - INFO - 下载市值数据: AAPL (2025-03-26)
2025-07-07 17:35:52,869 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-28.json
2025-07-07 17:35:52,870 - INFO - 下载市值数据: MSFT (2025-04-29)
2025-07-07 17:35:53,018 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-26.json
2025-07-07 17:35:53,019 - INFO - 下载市值数据: AAPL (2025-03-27)
2025-07-07 17:35:53,372 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-29.json
2025-07-07 17:35:53,373 - INFO - 下载市值数据: MSFT (2025-04-30)
2025-07-07 17:35:53,521 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-27.json
2025-07-07 17:35:53,522 - INFO - 下载市值数据: AAPL (2025-03-28)
2025-07-07 17:35:53,875 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-30.json
2025-07-07 17:35:53,875 - INFO - 下载市值数据: MSFT (2025-05-01)
2025-07-07 17:35:54,025 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-28.json
2025-07-07 17:35:54,026 - INFO - 下载市值数据: AAPL (2025-03-31)
2025-07-07 17:35:54,377 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-01.json
2025-07-07 17:35:54,378 - INFO - 下载市值数据: MSFT (2025-05-02)
2025-07-07 17:35:54,528 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-31.json
2025-07-07 17:35:54,529 - INFO - 下载市值数据: AAPL (2025-04-01)
2025-07-07 17:35:54,879 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-02.json
2025-07-07 17:35:54,880 - INFO - 下载市值数据: MSFT (2025-05-05)
2025-07-07 17:35:55,030 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-01.json
2025-07-07 17:35:55,030 - INFO - 下载市值数据: AAPL (2025-04-02)
2025-07-07 17:35:55,382 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-05.json
2025-07-07 17:35:55,382 - INFO - 下载市值数据: MSFT (2025-05-06)
2025-07-07 17:35:55,532 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-02.json
2025-07-07 17:35:55,533 - INFO - 下载市值数据: AAPL (2025-04-03)
2025-07-07 17:35:55,884 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-06.json
2025-07-07 17:35:55,885 - INFO - 下载市值数据: MSFT (2025-05-07)
2025-07-07 17:35:56,034 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-03.json
2025-07-07 17:35:56,034 - INFO - 下载市值数据: AAPL (2025-04-04)
2025-07-07 17:35:56,387 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-07.json
2025-07-07 17:35:56,387 - INFO - 下载市值数据: MSFT (2025-05-08)
2025-07-07 17:35:56,536 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-04.json
2025-07-07 17:35:56,537 - INFO - 下载市值数据: AAPL (2025-04-07)
2025-07-07 17:35:56,890 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-08.json
2025-07-07 17:35:56,891 - INFO - 下载市值数据: MSFT (2025-05-09)
2025-07-07 17:35:57,039 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-07.json
2025-07-07 17:35:57,041 - INFO - 下载市值数据: AAPL (2025-04-08)
2025-07-07 17:35:57,393 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-09.json
2025-07-07 17:35:57,394 - INFO - 下载市值数据: MSFT (2025-05-12)
2025-07-07 17:35:57,543 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-08.json
2025-07-07 17:35:57,544 - INFO - 下载市值数据: AAPL (2025-04-09)
2025-07-07 17:35:57,896 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-12.json
2025-07-07 17:35:57,896 - INFO - 下载市值数据: MSFT (2025-05-13)
2025-07-07 17:35:58,045 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-09.json
2025-07-07 17:35:58,046 - INFO - 下载市值数据: AAPL (2025-04-10)
2025-07-07 17:35:58,398 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-13.json
2025-07-07 17:35:58,398 - INFO - 下载市值数据: MSFT (2025-05-14)
2025-07-07 17:35:58,547 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-10.json
2025-07-07 17:35:58,548 - INFO - 下载市值数据: AAPL (2025-04-11)
2025-07-07 17:35:58,900 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-14.json
2025-07-07 17:35:58,902 - INFO - 下载市值数据: MSFT (2025-05-15)
2025-07-07 17:35:59,050 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-11.json
2025-07-07 17:35:59,050 - INFO - 下载市值数据: AAPL (2025-04-14)
2025-07-07 17:35:59,403 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-15.json
2025-07-07 17:35:59,403 - INFO - 下载市值数据: MSFT (2025-05-16)
2025-07-07 17:35:59,552 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-14.json
2025-07-07 17:35:59,552 - INFO - 下载市值数据: AAPL (2025-04-15)
2025-07-07 17:35:59,907 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-16.json
2025-07-07 17:35:59,908 - INFO - 下载市值数据: MSFT (2025-05-19)
2025-07-07 17:36:00,053 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-15.json
2025-07-07 17:36:00,053 - INFO - 下载市值数据: AAPL (2025-04-16)
2025-07-07 17:36:00,410 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-19.json
2025-07-07 17:36:00,410 - INFO - 下载市值数据: MSFT (2025-05-20)
2025-07-07 17:36:00,554 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-16.json
2025-07-07 17:36:00,555 - INFO - 下载市值数据: AAPL (2025-04-17)
2025-07-07 17:36:00,912 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-20.json
2025-07-07 17:36:00,912 - INFO - 下载市值数据: MSFT (2025-05-21)
2025-07-07 17:36:01,057 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-17.json
2025-07-07 17:36:01,058 - INFO - 下载市值数据: AAPL (2025-04-18)
2025-07-07 17:36:01,414 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-21.json
2025-07-07 17:36:01,415 - INFO - 下载市值数据: MSFT (2025-05-22)
2025-07-07 17:36:01,559 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-18.json
2025-07-07 17:36:01,560 - INFO - 下载市值数据: AAPL (2025-04-21)
2025-07-07 17:36:01,916 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-22.json
2025-07-07 17:36:01,916 - INFO - 下载市值数据: MSFT (2025-05-23)
2025-07-07 17:36:02,062 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-21.json
2025-07-07 17:36:02,062 - INFO - 下载市值数据: AAPL (2025-04-22)
2025-07-07 17:36:02,418 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-23.json
2025-07-07 17:36:02,419 - INFO - 下载市值数据: MSFT (2025-05-26)
2025-07-07 17:36:02,565 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-22.json
2025-07-07 17:36:02,566 - INFO - 下载市值数据: AAPL (2025-04-23)
2025-07-07 17:36:02,920 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-26.json
2025-07-07 17:36:02,921 - INFO - 下载市值数据: MSFT (2025-05-27)
2025-07-07 17:36:03,067 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-23.json
2025-07-07 17:36:03,067 - INFO - 下载市值数据: AAPL (2025-04-24)
2025-07-07 17:36:03,423 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-27.json
2025-07-07 17:36:03,424 - INFO - 下载市值数据: MSFT (2025-05-28)
2025-07-07 17:36:03,569 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-24.json
2025-07-07 17:36:03,570 - INFO - 下载市值数据: AAPL (2025-04-25)
2025-07-07 17:36:03,925 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-28.json
2025-07-07 17:36:03,926 - INFO - 下载市值数据: MSFT (2025-05-29)
2025-07-07 17:36:04,072 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-25.json
2025-07-07 17:36:04,073 - INFO - 下载市值数据: AAPL (2025-04-28)
2025-07-07 17:36:04,427 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-29.json
2025-07-07 17:36:04,428 - INFO - 下载市值数据: MSFT (2025-05-30)
2025-07-07 17:36:04,577 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-28.json
2025-07-07 17:36:04,578 - INFO - 下载市值数据: AAPL (2025-04-29)
2025-07-07 17:36:04,930 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-30.json
2025-07-07 17:36:04,930 - INFO - 下载市值数据: MSFT (2025-06-01)
2025-07-07 17:36:05,078 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-29.json
2025-07-07 17:36:05,079 - INFO - 下载市值数据: AAPL (2025-04-30)
2025-07-07 17:36:05,432 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-06-01.json
2025-07-07 17:36:05,432 - INFO - MSFT line_items: 生成 6 个采样日期
2025-07-07 17:36:05,432 - INFO - 下载财务科目数据: MSFT (2025-01-01)
2025-07-07 17:36:05,580 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-30.json
2025-07-07 17:36:05,582 - INFO - 下载市值数据: AAPL (2025-05-01)
2025-07-07 17:36:06,071 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:06,083 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-01.json
2025-07-07 17:36:06,083 - INFO - 下载市值数据: AAPL (2025-05-02)
2025-07-07 17:36:06,584 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-02.json
2025-07-07 17:36:06,585 - INFO - 下载市值数据: AAPL (2025-05-05)
2025-07-07 17:36:06,996 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:06,999 - DEBUG - 数据已保存到: financial_data_offline\MSFT_line_items\MSFT_line_items_2025-01-01.json
2025-07-07 17:36:07,000 - INFO - 下载财务科目数据: MSFT (2025-02-01)
2025-07-07 17:36:07,087 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-05.json
2025-07-07 17:36:07,088 - INFO - 下载市值数据: AAPL (2025-05-06)
2025-07-07 17:36:07,591 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-06.json
2025-07-07 17:36:07,592 - INFO - 下载市值数据: AAPL (2025-05-07)
2025-07-07 17:36:07,650 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:08,093 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-07.json
2025-07-07 17:36:08,094 - INFO - 下载市值数据: AAPL (2025-05-08)
2025-07-07 17:36:08,595 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-08.json
2025-07-07 17:36:08,596 - INFO - 下载市值数据: AAPL (2025-05-09)
2025-07-07 17:36:08,597 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:08,599 - DEBUG - 数据已保存到: financial_data_offline\MSFT_line_items\MSFT_line_items_2025-02-01.json
2025-07-07 17:36:08,600 - INFO - 下载财务科目数据: MSFT (2025-03-01)
2025-07-07 17:36:09,098 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-09.json
2025-07-07 17:36:09,099 - INFO - 下载市值数据: AAPL (2025-05-12)
2025-07-07 17:36:09,241 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:09,264 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:09,264 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:09,264 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:36:09,285 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:09,600 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-12.json
2025-07-07 17:36:09,600 - INFO - 下载市值数据: AAPL (2025-05-13)
2025-07-07 17:36:10,102 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-13.json
2025-07-07 17:36:10,103 - INFO - 下载市值数据: AAPL (2025-05-14)
2025-07-07 17:36:10,606 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-14.json
2025-07-07 17:36:10,606 - INFO - 下载市值数据: AAPL (2025-05-15)
2025-07-07 17:36:11,108 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-15.json
2025-07-07 17:36:11,109 - INFO - 下载市值数据: AAPL (2025-05-16)
2025-07-07 17:36:11,613 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-16.json
2025-07-07 17:36:11,613 - INFO - 下载市值数据: AAPL (2025-05-19)
2025-07-07 17:36:12,114 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-19.json
2025-07-07 17:36:12,114 - INFO - 下载市值数据: AAPL (2025-05-20)
2025-07-07 17:36:12,616 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-20.json
2025-07-07 17:36:12,617 - INFO - 下载市值数据: AAPL (2025-05-21)
2025-07-07 17:36:13,118 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-21.json
2025-07-07 17:36:13,119 - INFO - 下载市值数据: AAPL (2025-05-22)
2025-07-07 17:36:13,286 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:13,286 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:36:13,307 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:13,620 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-22.json
2025-07-07 17:36:13,621 - INFO - 下载市值数据: AAPL (2025-05-23)
2025-07-07 17:36:14,123 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-23.json
2025-07-07 17:36:14,123 - INFO - 下载市值数据: AAPL (2025-05-26)
2025-07-07 17:36:14,626 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-26.json
2025-07-07 17:36:14,626 - INFO - 下载市值数据: AAPL (2025-05-27)
2025-07-07 17:36:15,128 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-27.json
2025-07-07 17:36:15,128 - INFO - 下载市值数据: AAPL (2025-05-28)
2025-07-07 17:36:15,630 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-28.json
2025-07-07 17:36:15,632 - INFO - 下载市值数据: AAPL (2025-05-29)
2025-07-07 17:36:15,638 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 17:36:15,638 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:36:16,133 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-29.json
2025-07-07 17:36:16,134 - INFO - 下载市值数据: AAPL (2025-05-30)
2025-07-07 17:36:16,635 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-30.json
2025-07-07 17:36:16,636 - INFO - 下载市值数据: AAPL (2025-06-01)
2025-07-07 17:36:16,756 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=NVDA&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 17:36:17,138 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-06-01.json
2025-07-07 17:36:17,138 - INFO - AAPL line_items: 生成 6 个采样日期
2025-07-07 17:36:17,139 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 17:36:17,190 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-01.json
2025-07-07 17:36:17,191 - INFO - 下载公司新闻数据: NVDA (2025-01-02)
2025-07-07 17:36:17,694 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-02.json
2025-07-07 17:36:17,694 - INFO - 下载公司新闻数据: NVDA (2025-01-03)
2025-07-07 17:36:17,836 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:18,196 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-03.json
2025-07-07 17:36:18,196 - INFO - 下载公司新闻数据: NVDA (2025-01-06)
2025-07-07 17:36:18,700 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-06.json
2025-07-07 17:36:18,700 - INFO - 下载公司新闻数据: NVDA (2025-01-07)
2025-07-07 17:36:18,741 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:18,743 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-01.json
2025-07-07 17:36:18,743 - INFO - 下载财务科目数据: AAPL (2025-02-01)
2025-07-07 17:36:19,203 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-07.json
2025-07-07 17:36:19,203 - INFO - 下载公司新闻数据: NVDA (2025-01-08)
2025-07-07 17:36:19,384 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:19,706 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-08.json
2025-07-07 17:36:19,708 - INFO - 下载公司新闻数据: NVDA (2025-01-09)
2025-07-07 17:36:20,215 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-09.json
2025-07-07 17:36:20,215 - INFO - 下载公司新闻数据: NVDA (2025-01-10)
2025-07-07 17:36:20,301 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:20,302 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-02-01.json
2025-07-07 17:36:20,303 - INFO - 下载财务科目数据: AAPL (2025-03-01)
2025-07-07 17:36:20,716 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-10.json
2025-07-07 17:36:20,718 - INFO - 下载公司新闻数据: NVDA (2025-01-13)
2025-07-07 17:36:20,948 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:21,138 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:21,138 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:21,139 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:36:21,220 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-13.json
2025-07-07 17:36:21,220 - INFO - 下载公司新闻数据: NVDA (2025-01-14)
2025-07-07 17:36:21,308 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:21,309 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:36:21,725 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-14.json
2025-07-07 17:36:21,726 - INFO - 下载公司新闻数据: NVDA (2025-01-15)
2025-07-07 17:36:22,033 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:22,034 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-03-01.json
2025-07-07 17:36:22,035 - INFO - 下载财务科目数据: AAPL (2025-04-01)
2025-07-07 17:36:22,195 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:22,198 - DEBUG - 数据已保存到: financial_data_offline\MSFT_line_items\MSFT_line_items_2025-03-01.json
2025-07-07 17:36:22,199 - INFO - 下载财务科目数据: MSFT (2025-04-01)
2025-07-07 17:36:22,229 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-15.json
2025-07-07 17:36:22,229 - INFO - 下载公司新闻数据: NVDA (2025-01-16)
2025-07-07 17:36:22,733 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-16.json
2025-07-07 17:36:22,733 - INFO - 下载公司新闻数据: NVDA (2025-01-17)
2025-07-07 17:36:22,739 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:22,832 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:22,832 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:22,833 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:36:22,910 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:23,049 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:23,049 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:23,050 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:36:23,131 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:23,236 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-17.json
2025-07-07 17:36:23,236 - INFO - 下载公司新闻数据: NVDA (2025-01-20)
2025-07-07 17:36:23,743 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:23,746 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-20.json
2025-07-07 17:36:23,747 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-04-01.json
2025-07-07 17:36:23,747 - INFO - 下载公司新闻数据: NVDA (2025-01-21)
2025-07-07 17:36:23,748 - INFO - 下载财务科目数据: AAPL (2025-05-01)
2025-07-07 17:36:24,266 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-21.json
2025-07-07 17:36:24,266 - INFO - 下载公司新闻数据: NVDA (2025-01-22)
2025-07-07 17:36:24,432 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:24,527 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:24,528 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:24,529 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:36:24,636 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:24,772 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-22.json
2025-07-07 17:36:24,774 - INFO - 下载公司新闻数据: NVDA (2025-01-23)
2025-07-07 17:36:25,279 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-23.json
2025-07-07 17:36:25,280 - INFO - 下载公司新闻数据: NVDA (2025-01-24)
2025-07-07 17:36:25,783 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-24.json
2025-07-07 17:36:25,783 - INFO - 下载公司新闻数据: NVDA (2025-01-27)
2025-07-07 17:36:26,286 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-27.json
2025-07-07 17:36:26,289 - INFO - 下载公司新闻数据: NVDA (2025-01-28)
2025-07-07 17:36:26,794 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-28.json
2025-07-07 17:36:26,795 - INFO - 下载公司新闻数据: NVDA (2025-01-29)
2025-07-07 17:36:27,132 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:27,133 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:36:27,299 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-29.json
2025-07-07 17:36:27,300 - INFO - 下载公司新闻数据: NVDA (2025-01-30)
2025-07-07 17:36:27,337 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:27,804 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-30.json
2025-07-07 17:36:27,805 - INFO - 下载公司新闻数据: NVDA (2025-01-31)
2025-07-07 17:36:28,308 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-31.json
2025-07-07 17:36:28,308 - INFO - 下载公司新闻数据: NVDA (2025-02-03)
2025-07-07 17:36:28,637 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:28,638 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:36:28,715 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:28,812 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-03.json
2025-07-07 17:36:28,813 - INFO - 下载公司新闻数据: NVDA (2025-02-04)
2025-07-07 17:36:29,318 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-04.json
2025-07-07 17:36:29,318 - INFO - 下载公司新闻数据: NVDA (2025-02-05)
2025-07-07 17:36:29,824 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-05.json
2025-07-07 17:36:29,825 - INFO - 下载公司新闻数据: NVDA (2025-02-06)
2025-07-07 17:36:30,331 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-06.json
2025-07-07 17:36:30,331 - INFO - 下载公司新闻数据: NVDA (2025-02-07)
2025-07-07 17:36:30,833 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-07.json
2025-07-07 17:36:30,833 - INFO - 下载公司新闻数据: NVDA (2025-02-10)
2025-07-07 17:36:31,340 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-10.json
2025-07-07 17:36:31,341 - INFO - 下载公司新闻数据: NVDA (2025-02-11)
2025-07-07 17:36:31,844 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-11.json
2025-07-07 17:36:31,844 - INFO - 下载公司新闻数据: NVDA (2025-02-12)
2025-07-07 17:36:32,348 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-12.json
2025-07-07 17:36:32,349 - INFO - 下载公司新闻数据: NVDA (2025-02-13)
2025-07-07 17:36:32,853 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-13.json
2025-07-07 17:36:32,854 - INFO - 下载公司新闻数据: NVDA (2025-02-14)
2025-07-07 17:36:33,359 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-14.json
2025-07-07 17:36:33,360 - INFO - 下载公司新闻数据: NVDA (2025-02-17)
2025-07-07 17:36:33,864 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-17.json
2025-07-07 17:36:33,865 - INFO - 下载公司新闻数据: NVDA (2025-02-18)
2025-07-07 17:36:34,368 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-18.json
2025-07-07 17:36:34,369 - INFO - 下载公司新闻数据: NVDA (2025-02-19)
2025-07-07 17:36:34,877 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-19.json
2025-07-07 17:36:34,878 - INFO - 下载公司新闻数据: NVDA (2025-02-20)
2025-07-07 17:36:35,338 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:35,339 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:36:35,380 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-20.json
2025-07-07 17:36:35,381 - INFO - 下载公司新闻数据: NVDA (2025-02-21)
2025-07-07 17:36:35,392 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:35,885 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-21.json
2025-07-07 17:36:35,886 - INFO - 下载公司新闻数据: NVDA (2025-02-24)
2025-07-07 17:36:36,388 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-24.json
2025-07-07 17:36:36,388 - INFO - 下载公司新闻数据: NVDA (2025-02-25)
2025-07-07 17:36:36,716 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:36,716 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:36:36,894 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-25.json
2025-07-07 17:36:36,894 - INFO - 下载公司新闻数据: NVDA (2025-02-26)
2025-07-07 17:36:37,399 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-26.json
2025-07-07 17:36:37,400 - INFO - 下载公司新闻数据: NVDA (2025-02-27)
2025-07-07 17:36:37,681 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:37,683 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-05-01.json
2025-07-07 17:36:37,684 - INFO - 下载财务科目数据: AAPL (2025-06-01)
2025-07-07 17:36:37,908 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-27.json
2025-07-07 17:36:37,909 - INFO - 下载公司新闻数据: NVDA (2025-02-28)
2025-07-07 17:36:38,334 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:36:38,413 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-28.json
2025-07-07 17:36:38,414 - INFO - 下载公司新闻数据: NVDA (2025-03-03)
2025-07-07 17:36:38,919 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-03.json
2025-07-07 17:36:38,920 - INFO - 下载公司新闻数据: NVDA (2025-03-04)
2025-07-07 17:36:39,261 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:36:39,263 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-06-01.json
2025-07-07 17:36:39,263 - INFO - 完成下载: AAPL
2025-07-07 17:36:39,422 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-04.json
2025-07-07 17:36:39,423 - INFO - 下载公司新闻数据: NVDA (2025-03-05)
2025-07-07 17:36:39,929 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-05.json
2025-07-07 17:36:39,930 - INFO - 下载公司新闻数据: NVDA (2025-03-06)
2025-07-07 17:36:40,434 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-06.json
2025-07-07 17:36:40,435 - INFO - 下载公司新闻数据: NVDA (2025-03-07)
2025-07-07 17:36:40,942 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-07.json
2025-07-07 17:36:40,943 - INFO - 下载公司新闻数据: NVDA (2025-03-10)
2025-07-07 17:36:41,445 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-10.json
2025-07-07 17:36:41,445 - INFO - 下载公司新闻数据: NVDA (2025-03-11)
2025-07-07 17:36:41,948 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-11.json
2025-07-07 17:36:41,949 - INFO - 下载公司新闻数据: NVDA (2025-03-12)
2025-07-07 17:36:42,451 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-12.json
2025-07-07 17:36:42,452 - INFO - 下载公司新闻数据: NVDA (2025-03-13)
2025-07-07 17:36:42,958 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-13.json
2025-07-07 17:36:42,959 - INFO - 下载公司新闻数据: NVDA (2025-03-14)
2025-07-07 17:36:43,463 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-14.json
2025-07-07 17:36:43,464 - INFO - 下载公司新闻数据: NVDA (2025-03-17)
2025-07-07 17:36:43,968 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-17.json
2025-07-07 17:36:43,969 - INFO - 下载公司新闻数据: NVDA (2025-03-18)
2025-07-07 17:36:44,476 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-18.json
2025-07-07 17:36:44,476 - INFO - 下载公司新闻数据: NVDA (2025-03-19)
2025-07-07 17:36:44,981 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-19.json
2025-07-07 17:36:44,982 - INFO - 下载公司新闻数据: NVDA (2025-03-20)
2025-07-07 17:36:45,486 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-20.json
2025-07-07 17:36:45,487 - INFO - 下载公司新闻数据: NVDA (2025-03-21)
2025-07-07 17:36:45,994 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-21.json
2025-07-07 17:36:45,995 - INFO - 下载公司新闻数据: NVDA (2025-03-24)
2025-07-07 17:36:46,499 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-24.json
2025-07-07 17:36:46,500 - INFO - 下载公司新闻数据: NVDA (2025-03-25)
2025-07-07 17:36:47,005 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-25.json
2025-07-07 17:36:47,006 - INFO - 下载公司新闻数据: NVDA (2025-03-26)
2025-07-07 17:36:47,511 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-26.json
2025-07-07 17:36:47,512 - INFO - 下载公司新闻数据: NVDA (2025-03-27)
2025-07-07 17:36:48,015 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-27.json
2025-07-07 17:36:48,016 - INFO - 下载公司新闻数据: NVDA (2025-03-28)
2025-07-07 17:36:48,520 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-28.json
2025-07-07 17:36:48,521 - INFO - 下载公司新闻数据: NVDA (2025-03-31)
2025-07-07 17:36:49,024 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-31.json
2025-07-07 17:36:49,025 - INFO - 下载公司新闻数据: NVDA (2025-04-01)
2025-07-07 17:36:49,529 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-01.json
2025-07-07 17:36:49,530 - INFO - 下载公司新闻数据: NVDA (2025-04-02)
2025-07-07 17:36:50,033 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-02.json
2025-07-07 17:36:50,034 - INFO - 下载公司新闻数据: NVDA (2025-04-03)
2025-07-07 17:36:50,537 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-03.json
2025-07-07 17:36:50,538 - INFO - 下载公司新闻数据: NVDA (2025-04-04)
2025-07-07 17:36:51,041 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-04.json
2025-07-07 17:36:51,041 - INFO - 下载公司新闻数据: NVDA (2025-04-07)
2025-07-07 17:36:51,394 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:36:51,394 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:36:51,479 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:36:51,546 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-07.json
2025-07-07 17:36:51,546 - INFO - 下载公司新闻数据: NVDA (2025-04-08)
2025-07-07 17:36:52,049 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-08.json
2025-07-07 17:36:52,050 - INFO - 下载公司新闻数据: NVDA (2025-04-09)
2025-07-07 17:36:52,558 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-09.json
2025-07-07 17:36:52,559 - INFO - 下载公司新闻数据: NVDA (2025-04-10)
2025-07-07 17:36:53,062 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-10.json
2025-07-07 17:36:53,063 - INFO - 下载公司新闻数据: NVDA (2025-04-11)
2025-07-07 17:36:53,567 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-11.json
2025-07-07 17:36:53,567 - INFO - 下载公司新闻数据: NVDA (2025-04-14)
2025-07-07 17:36:54,072 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-14.json
2025-07-07 17:36:54,074 - INFO - 下载公司新闻数据: NVDA (2025-04-15)
2025-07-07 17:36:54,578 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-15.json
2025-07-07 17:36:54,578 - INFO - 下载公司新闻数据: NVDA (2025-04-16)
2025-07-07 17:36:55,083 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-16.json
2025-07-07 17:36:55,084 - INFO - 下载公司新闻数据: NVDA (2025-04-17)
2025-07-07 17:36:55,587 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-17.json
2025-07-07 17:36:55,587 - INFO - 下载公司新闻数据: NVDA (2025-04-18)
2025-07-07 17:36:56,092 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-18.json
2025-07-07 17:36:56,094 - INFO - 下载公司新闻数据: NVDA (2025-04-21)
2025-07-07 17:36:56,596 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-21.json
2025-07-07 17:36:56,598 - INFO - 下载公司新闻数据: NVDA (2025-04-22)
2025-07-07 17:36:57,102 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-22.json
2025-07-07 17:36:57,103 - INFO - 下载公司新闻数据: NVDA (2025-04-23)
2025-07-07 17:36:57,610 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-23.json
2025-07-07 17:36:57,610 - INFO - 下载公司新闻数据: NVDA (2025-04-24)
2025-07-07 17:36:58,115 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-24.json
2025-07-07 17:36:58,116 - INFO - 下载公司新闻数据: NVDA (2025-04-25)
2025-07-07 17:36:58,622 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-25.json
2025-07-07 17:36:58,623 - INFO - 下载公司新闻数据: NVDA (2025-04-28)
2025-07-07 17:36:59,126 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-28.json
2025-07-07 17:36:59,126 - INFO - 下载公司新闻数据: NVDA (2025-04-29)
2025-07-07 17:36:59,630 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-29.json
2025-07-07 17:36:59,630 - INFO - 下载公司新闻数据: NVDA (2025-04-30)
2025-07-07 17:37:00,133 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-30.json
2025-07-07 17:37:00,134 - INFO - 下载公司新闻数据: NVDA (2025-05-01)
2025-07-07 17:37:00,640 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-01.json
2025-07-07 17:37:00,642 - INFO - 下载公司新闻数据: NVDA (2025-05-02)
2025-07-07 17:37:01,146 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-02.json
2025-07-07 17:37:01,146 - INFO - 下载公司新闻数据: NVDA (2025-05-05)
2025-07-07 17:37:01,650 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-05.json
2025-07-07 17:37:01,650 - INFO - 下载公司新闻数据: NVDA (2025-05-06)
2025-07-07 17:37:02,154 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-06.json
2025-07-07 17:37:02,156 - INFO - 下载公司新闻数据: NVDA (2025-05-07)
2025-07-07 17:37:02,661 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-07.json
2025-07-07 17:37:02,662 - INFO - 下载公司新闻数据: NVDA (2025-05-08)
2025-07-07 17:37:03,166 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-08.json
2025-07-07 17:37:03,167 - INFO - 下载公司新闻数据: NVDA (2025-05-09)
2025-07-07 17:37:03,672 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-09.json
2025-07-07 17:37:03,673 - INFO - 下载公司新闻数据: NVDA (2025-05-12)
2025-07-07 17:37:04,178 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-12.json
2025-07-07 17:37:04,180 - INFO - 下载公司新闻数据: NVDA (2025-05-13)
2025-07-07 17:37:04,684 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-13.json
2025-07-07 17:37:04,684 - INFO - 下载公司新闻数据: NVDA (2025-05-14)
2025-07-07 17:37:05,190 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-14.json
2025-07-07 17:37:05,191 - INFO - 下载公司新闻数据: NVDA (2025-05-15)
2025-07-07 17:37:05,696 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-15.json
2025-07-07 17:37:05,697 - INFO - 下载公司新闻数据: NVDA (2025-05-16)
2025-07-07 17:37:06,200 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-16.json
2025-07-07 17:37:06,200 - INFO - 下载公司新闻数据: NVDA (2025-05-19)
2025-07-07 17:37:06,702 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-19.json
2025-07-07 17:37:06,703 - INFO - 下载公司新闻数据: NVDA (2025-05-20)
2025-07-07 17:37:07,208 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-20.json
2025-07-07 17:37:07,209 - INFO - 下载公司新闻数据: NVDA (2025-05-21)
2025-07-07 17:37:07,713 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-21.json
2025-07-07 17:37:07,713 - INFO - 下载公司新闻数据: NVDA (2025-05-22)
2025-07-07 17:37:08,217 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-22.json
2025-07-07 17:37:08,217 - INFO - 下载公司新闻数据: NVDA (2025-05-23)
2025-07-07 17:37:08,721 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-23.json
2025-07-07 17:37:08,722 - INFO - 下载公司新闻数据: NVDA (2025-05-26)
2025-07-07 17:37:09,226 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-26.json
2025-07-07 17:37:09,228 - INFO - 下载公司新闻数据: NVDA (2025-05-27)
2025-07-07 17:37:09,731 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-27.json
2025-07-07 17:37:09,731 - INFO - 下载公司新闻数据: NVDA (2025-05-28)
2025-07-07 17:37:10,235 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-28.json
2025-07-07 17:37:10,236 - INFO - 下载公司新闻数据: NVDA (2025-05-29)
2025-07-07 17:37:10,741 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-29.json
2025-07-07 17:37:10,742 - INFO - 下载公司新闻数据: NVDA (2025-05-30)
2025-07-07 17:37:11,246 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-30.json
2025-07-07 17:37:11,247 - INFO - 下载公司新闻数据: NVDA (2025-06-01)
2025-07-07 17:37:11,751 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-06-01.json
2025-07-07 17:37:11,754 - INFO - NVDA market_cap: 生成 109 个采样日期
2025-07-07 17:37:11,756 - INFO - 下载市值数据: NVDA (2025-01-01)
2025-07-07 17:37:12,259 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-01.json
2025-07-07 17:37:12,261 - INFO - 下载市值数据: NVDA (2025-01-02)
2025-07-07 17:37:12,763 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-02.json
2025-07-07 17:37:12,764 - INFO - 下载市值数据: NVDA (2025-01-03)
2025-07-07 17:37:13,266 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-03.json
2025-07-07 17:37:13,266 - INFO - 下载市值数据: NVDA (2025-01-06)
2025-07-07 17:37:13,769 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-06.json
2025-07-07 17:37:13,770 - INFO - 下载市值数据: NVDA (2025-01-07)
2025-07-07 17:37:14,272 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-07.json
2025-07-07 17:37:14,273 - INFO - 下载市值数据: NVDA (2025-01-08)
2025-07-07 17:37:14,775 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-08.json
2025-07-07 17:37:14,775 - INFO - 下载市值数据: NVDA (2025-01-09)
2025-07-07 17:37:15,277 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-09.json
2025-07-07 17:37:15,278 - INFO - 下载市值数据: NVDA (2025-01-10)
2025-07-07 17:37:15,779 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-10.json
2025-07-07 17:37:15,779 - INFO - 下载市值数据: NVDA (2025-01-13)
2025-07-07 17:37:16,283 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-13.json
2025-07-07 17:37:16,283 - INFO - 下载市值数据: NVDA (2025-01-14)
2025-07-07 17:37:16,784 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-14.json
2025-07-07 17:37:16,785 - INFO - 下载市值数据: NVDA (2025-01-15)
2025-07-07 17:37:17,286 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-15.json
2025-07-07 17:37:17,288 - INFO - 下载市值数据: NVDA (2025-01-16)
2025-07-07 17:37:17,790 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-16.json
2025-07-07 17:37:17,791 - INFO - 下载市值数据: NVDA (2025-01-17)
2025-07-07 17:37:18,293 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-17.json
2025-07-07 17:37:18,294 - INFO - 下载市值数据: NVDA (2025-01-20)
2025-07-07 17:37:18,796 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-20.json
2025-07-07 17:37:18,796 - INFO - 下载市值数据: NVDA (2025-01-21)
2025-07-07 17:37:19,299 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-21.json
2025-07-07 17:37:19,300 - INFO - 下载市值数据: NVDA (2025-01-22)
2025-07-07 17:37:19,802 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-22.json
2025-07-07 17:37:19,803 - INFO - 下载市值数据: NVDA (2025-01-23)
2025-07-07 17:37:20,306 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-23.json
2025-07-07 17:37:20,308 - INFO - 下载市值数据: NVDA (2025-01-24)
2025-07-07 17:37:20,810 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-24.json
2025-07-07 17:37:20,810 - INFO - 下载市值数据: NVDA (2025-01-27)
2025-07-07 17:37:21,313 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-27.json
2025-07-07 17:37:21,314 - INFO - 下载市值数据: NVDA (2025-01-28)
2025-07-07 17:37:21,815 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-28.json
2025-07-07 17:37:21,816 - INFO - 下载市值数据: NVDA (2025-01-29)
2025-07-07 17:37:22,319 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-29.json
2025-07-07 17:37:22,319 - INFO - 下载市值数据: NVDA (2025-01-30)
2025-07-07 17:37:22,821 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-30.json
2025-07-07 17:37:22,822 - INFO - 下载市值数据: NVDA (2025-01-31)
2025-07-07 17:37:23,325 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-31.json
2025-07-07 17:37:23,326 - INFO - 下载市值数据: NVDA (2025-02-03)
2025-07-07 17:37:23,480 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:37:23,480 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:37:23,828 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-03.json
2025-07-07 17:37:23,828 - INFO - 下载市值数据: NVDA (2025-02-04)
2025-07-07 17:37:24,331 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-04.json
2025-07-07 17:37:24,332 - INFO - 下载市值数据: NVDA (2025-02-05)
2025-07-07 17:37:24,477 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:37:24,478 - DEBUG - 数据已保存到: financial_data_offline\MSFT_line_items\MSFT_line_items_2025-04-01.json
2025-07-07 17:37:24,479 - INFO - 下载财务科目数据: MSFT (2025-05-01)
2025-07-07 17:37:24,834 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-05.json
2025-07-07 17:37:24,835 - INFO - 下载市值数据: NVDA (2025-02-06)
2025-07-07 17:37:25,111 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:37:25,337 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-06.json
2025-07-07 17:37:25,337 - INFO - 下载市值数据: NVDA (2025-02-07)
2025-07-07 17:37:25,841 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-07.json
2025-07-07 17:37:25,842 - INFO - 下载市值数据: NVDA (2025-02-10)
2025-07-07 17:37:26,241 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:37:26,245 - DEBUG - 数据已保存到: financial_data_offline\MSFT_line_items\MSFT_line_items_2025-05-01.json
2025-07-07 17:37:26,246 - INFO - 下载财务科目数据: MSFT (2025-06-01)
2025-07-07 17:37:26,344 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-10.json
2025-07-07 17:37:26,345 - INFO - 下载市值数据: NVDA (2025-02-11)
2025-07-07 17:37:26,846 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-11.json
2025-07-07 17:37:26,846 - INFO - 下载市值数据: NVDA (2025-02-12)
2025-07-07 17:37:26,880 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:37:27,349 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-12.json
2025-07-07 17:37:27,350 - INFO - 下载市值数据: NVDA (2025-02-13)
2025-07-07 17:37:27,498 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:37:27,498 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:37:27,499 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:37:27,581 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:37:27,852 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-13.json
2025-07-07 17:37:27,853 - INFO - 下载市值数据: NVDA (2025-02-14)
2025-07-07 17:37:28,358 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-14.json
2025-07-07 17:37:28,359 - INFO - 下载市值数据: NVDA (2025-02-17)
2025-07-07 17:37:28,861 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-17.json
2025-07-07 17:37:28,862 - INFO - 下载市值数据: NVDA (2025-02-18)
2025-07-07 17:37:29,366 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-18.json
2025-07-07 17:37:29,367 - INFO - 下载市值数据: NVDA (2025-02-19)
2025-07-07 17:37:29,869 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-19.json
2025-07-07 17:37:29,870 - INFO - 下载市值数据: NVDA (2025-02-20)
2025-07-07 17:37:30,371 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-20.json
2025-07-07 17:37:30,373 - INFO - 下载市值数据: NVDA (2025-02-21)
2025-07-07 17:37:30,875 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-21.json
2025-07-07 17:37:30,876 - INFO - 下载市值数据: NVDA (2025-02-24)
2025-07-07 17:37:31,378 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-24.json
2025-07-07 17:37:31,379 - INFO - 下载市值数据: NVDA (2025-02-25)
2025-07-07 17:37:31,583 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:37:31,583 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:37:31,880 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-25.json
2025-07-07 17:37:31,881 - INFO - 下载市值数据: NVDA (2025-02-26)
2025-07-07 17:37:32,385 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-26.json
2025-07-07 17:37:32,385 - INFO - 下载市值数据: NVDA (2025-02-27)
2025-07-07 17:37:32,524 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:37:32,888 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-27.json
2025-07-07 17:37:32,889 - INFO - 下载市值数据: NVDA (2025-02-28)
2025-07-07 17:37:33,391 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-28.json
2025-07-07 17:37:33,392 - INFO - 下载市值数据: NVDA (2025-03-03)
2025-07-07 17:37:33,895 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-03.json
2025-07-07 17:37:33,896 - INFO - 下载市值数据: NVDA (2025-03-04)
2025-07-07 17:37:34,397 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-04.json
2025-07-07 17:37:34,398 - INFO - 下载市值数据: NVDA (2025-03-05)
2025-07-07 17:37:34,899 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-05.json
2025-07-07 17:37:34,900 - INFO - 下载市值数据: NVDA (2025-03-06)
2025-07-07 17:37:35,401 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-06.json
2025-07-07 17:37:35,402 - INFO - 下载市值数据: NVDA (2025-03-07)
2025-07-07 17:37:35,905 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-07.json
2025-07-07 17:37:35,907 - INFO - 下载市值数据: NVDA (2025-03-10)
2025-07-07 17:37:36,408 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-10.json
2025-07-07 17:37:36,409 - INFO - 下载市值数据: NVDA (2025-03-11)
2025-07-07 17:37:36,912 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-11.json
2025-07-07 17:37:36,913 - INFO - 下载市值数据: NVDA (2025-03-12)
2025-07-07 17:37:37,415 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-12.json
2025-07-07 17:37:37,416 - INFO - 下载市值数据: NVDA (2025-03-13)
2025-07-07 17:37:37,917 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-13.json
2025-07-07 17:37:37,918 - INFO - 下载市值数据: NVDA (2025-03-14)
2025-07-07 17:37:38,420 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-14.json
2025-07-07 17:37:38,421 - INFO - 下载市值数据: NVDA (2025-03-17)
2025-07-07 17:37:38,923 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-17.json
2025-07-07 17:37:38,925 - INFO - 下载市值数据: NVDA (2025-03-18)
2025-07-07 17:37:39,427 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-18.json
2025-07-07 17:37:39,429 - INFO - 下载市值数据: NVDA (2025-03-19)
2025-07-07 17:37:39,934 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-19.json
2025-07-07 17:37:39,934 - INFO - 下载市值数据: NVDA (2025-03-20)
2025-07-07 17:37:40,435 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-20.json
2025-07-07 17:37:40,436 - INFO - 下载市值数据: NVDA (2025-03-21)
2025-07-07 17:37:40,525 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:37:40,526 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:37:40,608 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:37:40,938 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-21.json
2025-07-07 17:37:40,939 - INFO - 下载市值数据: NVDA (2025-03-24)
2025-07-07 17:37:41,441 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-24.json
2025-07-07 17:37:41,442 - INFO - 下载市值数据: NVDA (2025-03-25)
2025-07-07 17:37:41,944 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-25.json
2025-07-07 17:37:41,945 - INFO - 下载市值数据: NVDA (2025-03-26)
2025-07-07 17:37:42,445 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-26.json
2025-07-07 17:37:42,446 - INFO - 下载市值数据: NVDA (2025-03-27)
2025-07-07 17:37:42,948 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-27.json
2025-07-07 17:37:42,949 - INFO - 下载市值数据: NVDA (2025-03-28)
2025-07-07 17:37:43,449 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-28.json
2025-07-07 17:37:43,449 - INFO - 下载市值数据: NVDA (2025-03-31)
2025-07-07 17:37:43,951 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-31.json
2025-07-07 17:37:43,952 - INFO - 下载市值数据: NVDA (2025-04-01)
2025-07-07 17:37:44,453 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-01.json
2025-07-07 17:37:44,454 - INFO - 下载市值数据: NVDA (2025-04-02)
2025-07-07 17:37:44,956 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-02.json
2025-07-07 17:37:44,956 - INFO - 下载市值数据: NVDA (2025-04-03)
2025-07-07 17:37:45,459 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-03.json
2025-07-07 17:37:45,459 - INFO - 下载市值数据: NVDA (2025-04-04)
2025-07-07 17:37:45,961 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-04.json
2025-07-07 17:37:45,961 - INFO - 下载市值数据: NVDA (2025-04-07)
2025-07-07 17:37:46,462 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-07.json
2025-07-07 17:37:46,463 - INFO - 下载市值数据: NVDA (2025-04-08)
2025-07-07 17:37:46,966 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-08.json
2025-07-07 17:37:46,967 - INFO - 下载市值数据: NVDA (2025-04-09)
2025-07-07 17:37:47,468 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-09.json
2025-07-07 17:37:47,469 - INFO - 下载市值数据: NVDA (2025-04-10)
2025-07-07 17:37:47,970 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-10.json
2025-07-07 17:37:47,971 - INFO - 下载市值数据: NVDA (2025-04-11)
2025-07-07 17:37:48,474 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-11.json
2025-07-07 17:37:48,475 - INFO - 下载市值数据: NVDA (2025-04-14)
2025-07-07 17:37:48,977 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-14.json
2025-07-07 17:37:48,978 - INFO - 下载市值数据: NVDA (2025-04-15)
2025-07-07 17:37:49,478 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-15.json
2025-07-07 17:37:49,479 - INFO - 下载市值数据: NVDA (2025-04-16)
2025-07-07 17:37:49,980 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-16.json
2025-07-07 17:37:49,981 - INFO - 下载市值数据: NVDA (2025-04-17)
2025-07-07 17:37:50,482 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-17.json
2025-07-07 17:37:50,483 - INFO - 下载市值数据: NVDA (2025-04-18)
2025-07-07 17:37:50,984 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-18.json
2025-07-07 17:37:50,985 - INFO - 下载市值数据: NVDA (2025-04-21)
2025-07-07 17:37:51,486 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-21.json
2025-07-07 17:37:51,486 - INFO - 下载市值数据: NVDA (2025-04-22)
2025-07-07 17:37:51,990 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-22.json
2025-07-07 17:37:51,991 - INFO - 下载市值数据: NVDA (2025-04-23)
2025-07-07 17:37:52,492 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-23.json
2025-07-07 17:37:52,493 - INFO - 下载市值数据: NVDA (2025-04-24)
2025-07-07 17:37:52,994 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-24.json
2025-07-07 17:37:52,995 - INFO - 下载市值数据: NVDA (2025-04-25)
2025-07-07 17:37:53,497 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-25.json
2025-07-07 17:37:53,497 - INFO - 下载市值数据: NVDA (2025-04-28)
2025-07-07 17:37:53,999 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-28.json
2025-07-07 17:37:54,000 - INFO - 下载市值数据: NVDA (2025-04-29)
2025-07-07 17:37:54,501 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-29.json
2025-07-07 17:37:54,502 - INFO - 下载市值数据: NVDA (2025-04-30)
2025-07-07 17:37:55,004 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-30.json
2025-07-07 17:37:55,005 - INFO - 下载市值数据: NVDA (2025-05-01)
2025-07-07 17:37:55,508 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-01.json
2025-07-07 17:37:55,509 - INFO - 下载市值数据: NVDA (2025-05-02)
2025-07-07 17:37:56,010 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-02.json
2025-07-07 17:37:56,011 - INFO - 下载市值数据: NVDA (2025-05-05)
2025-07-07 17:37:56,513 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-05.json
2025-07-07 17:37:56,514 - INFO - 下载市值数据: NVDA (2025-05-06)
2025-07-07 17:37:56,610 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:37:56,611 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:37:56,632 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:37:57,016 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-06.json
2025-07-07 17:37:57,016 - INFO - 下载市值数据: NVDA (2025-05-07)
2025-07-07 17:37:57,518 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-07.json
2025-07-07 17:37:57,519 - INFO - 下载市值数据: NVDA (2025-05-08)
2025-07-07 17:37:58,021 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-08.json
2025-07-07 17:37:58,023 - INFO - 下载市值数据: NVDA (2025-05-09)
2025-07-07 17:37:58,525 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-09.json
2025-07-07 17:37:58,526 - INFO - 下载市值数据: NVDA (2025-05-12)
2025-07-07 17:37:59,028 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-12.json
2025-07-07 17:37:59,029 - INFO - 下载市值数据: NVDA (2025-05-13)
2025-07-07 17:37:59,530 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-13.json
2025-07-07 17:37:59,530 - INFO - 下载市值数据: NVDA (2025-05-14)
2025-07-07 17:38:00,032 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-14.json
2025-07-07 17:38:00,032 - INFO - 下载市值数据: NVDA (2025-05-15)
2025-07-07 17:38:00,533 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-15.json
2025-07-07 17:38:00,533 - INFO - 下载市值数据: NVDA (2025-05-16)
2025-07-07 17:38:01,036 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-16.json
2025-07-07 17:38:01,036 - INFO - 下载市值数据: NVDA (2025-05-19)
2025-07-07 17:38:01,538 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-19.json
2025-07-07 17:38:01,539 - INFO - 下载市值数据: NVDA (2025-05-20)
2025-07-07 17:38:02,041 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-20.json
2025-07-07 17:38:02,042 - INFO - 下载市值数据: NVDA (2025-05-21)
2025-07-07 17:38:02,545 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-21.json
2025-07-07 17:38:02,545 - INFO - 下载市值数据: NVDA (2025-05-22)
2025-07-07 17:38:03,047 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-22.json
2025-07-07 17:38:03,048 - INFO - 下载市值数据: NVDA (2025-05-23)
2025-07-07 17:38:03,549 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-23.json
2025-07-07 17:38:03,549 - INFO - 下载市值数据: NVDA (2025-05-26)
2025-07-07 17:38:04,052 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-26.json
2025-07-07 17:38:04,052 - INFO - 下载市值数据: NVDA (2025-05-27)
2025-07-07 17:38:04,553 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-27.json
2025-07-07 17:38:04,553 - INFO - 下载市值数据: NVDA (2025-05-28)
2025-07-07 17:38:05,056 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-28.json
2025-07-07 17:38:05,058 - INFO - 下载市值数据: NVDA (2025-05-29)
2025-07-07 17:38:05,560 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-29.json
2025-07-07 17:38:05,561 - INFO - 下载市值数据: NVDA (2025-05-30)
2025-07-07 17:38:06,062 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-30.json
2025-07-07 17:38:06,062 - INFO - 下载市值数据: NVDA (2025-06-01)
2025-07-07 17:38:06,563 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-06-01.json
2025-07-07 17:38:06,564 - INFO - NVDA line_items: 生成 6 个采样日期
2025-07-07 17:38:06,564 - INFO - 下载财务科目数据: NVDA (2025-01-01)
2025-07-07 17:38:07,209 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:38:07,300 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:38:07,300 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:07,301 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:38:08,255 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:08,258 - DEBUG - 数据已保存到: financial_data_offline\NVDA_line_items\NVDA_line_items_2025-01-01.json
2025-07-07 17:38:08,259 - INFO - 下载财务科目数据: NVDA (2025-02-01)
2025-07-07 17:38:08,911 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:38:09,844 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:09,848 - DEBUG - 数据已保存到: financial_data_offline\NVDA_line_items\NVDA_line_items_2025-02-01.json
2025-07-07 17:38:09,849 - INFO - 下载财务科目数据: NVDA (2025-03-01)
2025-07-07 17:38:10,501 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:38:11,368 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:11,369 - DEBUG - 数据已保存到: financial_data_offline\NVDA_line_items\NVDA_line_items_2025-03-01.json
2025-07-07 17:38:11,369 - INFO - 下载财务科目数据: NVDA (2025-04-01)
2025-07-07 17:38:12,006 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:38:12,094 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:38:12,094 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:12,094 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:38:13,059 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:13,063 - DEBUG - 数据已保存到: financial_data_offline\NVDA_line_items\NVDA_line_items_2025-04-01.json
2025-07-07 17:38:13,063 - INFO - 下载财务科目数据: NVDA (2025-05-01)
2025-07-07 17:38:13,746 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:38:13,813 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:38:13,813 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:13,813 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:38:14,744 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:14,747 - DEBUG - 数据已保存到: financial_data_offline\NVDA_line_items\NVDA_line_items_2025-05-01.json
2025-07-07 17:38:14,748 - INFO - 下载财务科目数据: NVDA (2025-06-01)
2025-07-07 17:38:15,396 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:38:16,358 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:16,360 - DEBUG - 数据已保存到: financial_data_offline\NVDA_line_items\NVDA_line_items_2025-06-01.json
2025-07-07 17:38:16,360 - INFO - 完成下载: NVDA
2025-07-07 17:38:28,633 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:28,634 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:38:29,707 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:38:30,763 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:38:30,763 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:30,764 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:38:30,847 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:38:34,848 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:34,849 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:38:34,947 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:38:42,947 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:38:42,948 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:38:43,926 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:38:43,929 - DEBUG - 数据已保存到: financial_data_offline\MSFT_line_items\MSFT_line_items_2025-06-01.json
2025-07-07 17:38:43,929 - INFO - 完成下载: MSFT
2025-07-07 17:38:43,937 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_173843.txt
