2025-07-07 16:55:40,804 - INFO - 开始下载数据: 3 个股票, 6 种数据类型
2025-07-07 16:55:40,806 - INFO - 时间范围: 2025-01-01 到 2025-06-01
2025-07-07 16:55:40,806 - INFO - 数据类型: prices, financial_metrics, insider_trades, company_news, market_cap, line_items
2025-07-07 16:55:40,807 - INFO - 下载股价数据: AAPL (2025-01-01 to 2025-06-01)
2025-07-07 16:55:40,807 - INFO - 下载股价数据: MSFT (2025-01-01 to 2025-06-01)
2025-07-07 16:55:40,808 - INFO - 下载股价数据: NVDA (2025-01-01 to 2025-06-01)
2025-07-07 16:55:41,600 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:42,878 - INFO - 下载财务指标数据: AAPL (2025-01-01)
2025-07-07 16:55:42,880 - INFO - 下载财务指标数据: MSFT (2025-01-01)
2025-07-07 16:55:43,615 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:43,689 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:45,640 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:47,688 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:47,801 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:53,709 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:55,786 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:56,042 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:09,761 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:56:11,892 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:12,276 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:41,842 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:56:41,985 - ERROR - 下载股价数据失败 NVDA: HTTPSConnectionPool(host='api.financialdatasets.ai', port=443): Max retries exceeded with url: /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
2025-07-07 16:56:41,988 - INFO - 下载财务指标数据: NVDA (2025-01-01)
2025-07-07 16:56:43,829 - INFO - 下载内幕交易数据: NVDA (2025-01-01)
2025-07-07 16:56:44,555 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:44,580 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:44,872 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:45,719 - INFO - 下载内幕交易数据: AAPL (2025-01-01)
2025-07-07 16:56:46,028 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:46,389 - INFO - 下载公司新闻数据: NVDA (2025-01-01)
2025-07-07 16:56:47,113 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:56:47,540 - INFO - 下载公司新闻数据: AAPL (2025-01-01)
2025-07-07 16:56:49,630 - INFO - 下载市值数据: AAPL (2025-01-01)
2025-07-07 16:56:50,129 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:50,134 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 16:56:51,248 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:56:51,296 - INFO - 下载内幕交易数据: MSFT (2025-01-01)
2025-07-07 16:56:51,672 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:56:51,672 - INFO - 下载财务指标数据: AAPL (2025-01-08)
2025-07-07 16:56:51,978 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:52,176 - INFO - 下载内幕交易数据: AAPL (2025-01-08)
2025-07-07 16:56:52,679 - INFO - 下载公司新闻数据: AAPL (2025-01-08)
2025-07-07 16:56:53,182 - INFO - 下载市值数据: AAPL (2025-01-08)
2025-07-07 16:56:53,684 - INFO - 下载财务科目数据: AAPL (2025-01-08)
2025-07-07 16:56:55,270 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:56:55,271 - INFO - 下载财务指标数据: AAPL (2025-01-15)
2025-07-07 16:56:55,774 - INFO - 下载内幕交易数据: AAPL (2025-01-15)
2025-07-07 16:56:56,029 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:56,279 - INFO - 下载公司新闻数据: AAPL (2025-01-15)
2025-07-07 16:56:56,782 - INFO - 下载市值数据: AAPL (2025-01-15)
2025-07-07 16:56:57,284 - INFO - 下载财务科目数据: AAPL (2025-01-15)
2025-07-07 16:56:57,931 - INFO - 下载公司新闻数据: MSFT (2025-01-01)
2025-07-07 16:56:57,939 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:56:58,665 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=MSFT&end_date=2025-01-01&limit=100
2025-07-07 16:56:59,542 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:58:43,969 - INFO - 开始下载数据: 3 个股票, 6 种数据类型
2025-07-07 16:58:43,969 - INFO - 时间范围: 2025-01-01 到 2025-06-01
2025-07-07 16:58:43,969 - INFO - 数据类型: prices, financial_metrics, insider_trades, company_news, market_cap, line_items
2025-07-07 16:58:43,970 - INFO - 股价数据已存在: AAPL (2025-01-01 to 2025-06-01)
2025-07-07 16:58:43,971 - INFO - 股价数据已存在: MSFT (2025-01-01 to 2025-06-01)
2025-07-07 16:58:43,971 - INFO - 财务指标数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,971 - INFO - 下载股价数据: NVDA (2025-01-01 to 2025-06-01)
2025-07-07 16:58:43,971 - INFO - 财务指标数据已存在: MSFT (2025-01-01)
2025-07-07 16:58:43,971 - INFO - 内幕交易数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 内幕交易数据已存在: MSFT (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 公司新闻数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 下载公司新闻数据: MSFT (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 市值数据已存在: AAPL (2025-01-01)
2025-07-07 16:58:43,972 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 16:58:44,690 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:44,703 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:44,710 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:45,609 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:45,610 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:45,611 - INFO - 财务指标数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,612 - INFO - 内幕交易数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,612 - INFO - 公司新闻数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,612 - INFO - 市值数据已存在: AAPL (2025-01-08)
2025-07-07 16:58:45,613 - INFO - 下载财务科目数据: AAPL (2025-01-08)
2025-07-07 16:58:46,004 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=MSFT&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 16:58:46,134 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 HTTP/1.1" 200 None
2025-07-07 16:58:46,251 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:46,279 - DEBUG - 数据已保存到: financial_data_offline\NVDA_prices\NVDA_prices_2025-01-01_to_2025-06-01.json
2025-07-07 16:58:46,280 - INFO - 财务指标数据已存在: NVDA (2025-01-01)
2025-07-07 16:58:46,280 - INFO - 内幕交易数据已存在: NVDA (2025-01-01)
2025-07-07 16:58:46,280 - INFO - 下载公司新闻数据: NVDA (2025-01-01)
2025-07-07 16:58:46,446 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-01.json
2025-07-07 16:58:46,446 - INFO - 下载市值数据: MSFT (2025-01-01)
2025-07-07 16:58:46,920 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:47,078 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:47,163 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:47,164 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:47,165 - INFO - 财务指标数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,166 - INFO - 内幕交易数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,166 - INFO - 公司新闻数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,167 - INFO - 市值数据已存在: AAPL (2025-01-15)
2025-07-07 16:58:47,167 - INFO - 下载财务科目数据: AAPL (2025-01-15)
2025-07-07 16:58:47,222 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:47,223 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:58:47,224 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:58:47,398 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:47,801 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:47,883 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:47,883 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:58:47,885 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:58:47,952 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=NVDA&end_date=2025-01-01&limit=100 HTTP/1.1" 200 None
2025-07-07 16:58:47,965 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:48,399 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-01.json
2025-07-07 16:58:48,400 - INFO - 下载市值数据: NVDA (2025-01-01)
2025-07-07 16:58:49,039 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:49,931 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=NVDA&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 16:58:50,159 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-01.json
2025-07-07 16:58:50,159 - INFO - 下载财务科目数据: NVDA (2025-01-01)
2025-07-07 16:58:50,795 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:51,398 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:58:51,398 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:58:51,966 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:58:51,966 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:58:52,116 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:52,117 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:52,118 - INFO - 下载财务指标数据: NVDA (2025-01-08)
2025-07-07 16:58:52,231 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:58:52,353 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 16:58:52,604 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-01.json
2025-07-07 16:58:52,605 - INFO - 下载财务科目数据: MSFT (2025-01-01)
2025-07-07 16:58:52,621 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-08.json
2025-07-07 16:58:52,622 - INFO - 下载内幕交易数据: NVDA (2025-01-08)
2025-07-07 16:58:53,298 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:53,310 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:54,208 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:54,210 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:54,211 - INFO - 下载财务指标数据: MSFT (2025-01-08)
2025-07-07 16:58:54,714 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-08.json
2025-07-07 16:58:54,715 - INFO - 下载内幕交易数据: MSFT (2025-01-08)
2025-07-07 16:58:54,952 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-08&limit=50 HTTP/1.1" 200 None
2025-07-07 16:58:55,168 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-08.json
2025-07-07 16:58:55,169 - INFO - 下载公司新闻数据: NVDA (2025-01-08)
2025-07-07 16:58:55,354 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:55,673 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-08.json
2025-07-07 16:58:55,674 - INFO - 下载市值数据: NVDA (2025-01-08)
2025-07-07 16:58:56,176 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-08.json
2025-07-07 16:58:56,178 - INFO - 下载财务科目数据: NVDA (2025-01-08)
2025-07-07 16:58:56,976 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-08&limit=50 HTTP/1.1" 200 None
2025-07-07 16:58:57,003 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:57,200 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-08.json
2025-07-07 16:58:57,201 - INFO - 下载公司新闻数据: MSFT (2025-01-08)
2025-07-07 16:58:57,705 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-08.json
2025-07-07 16:58:57,707 - INFO - 下载市值数据: MSFT (2025-01-08)
2025-07-07 16:58:57,915 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:57,915 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:57,916 - INFO - 下载财务指标数据: NVDA (2025-01-15)
2025-07-07 16:58:58,208 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-08.json
2025-07-07 16:58:58,208 - INFO - 下载财务科目数据: MSFT (2025-01-08)
2025-07-07 16:58:58,417 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-15.json
2025-07-07 16:58:58,418 - INFO - 下载内幕交易数据: NVDA (2025-01-15)
2025-07-07 16:58:58,860 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:58:58,920 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-15.json
2025-07-07 16:58:58,921 - INFO - 下载公司新闻数据: NVDA (2025-01-15)
2025-07-07 16:58:59,425 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-15.json
2025-07-07 16:58:59,425 - INFO - 下载市值数据: NVDA (2025-01-15)
2025-07-07 16:58:59,845 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:58:59,846 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:58:59,847 - INFO - 下载财务指标数据: MSFT (2025-01-15)
2025-07-07 16:58:59,927 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-15.json
2025-07-07 16:58:59,928 - INFO - 下载财务科目数据: NVDA (2025-01-15)
2025-07-07 16:59:00,232 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:00,233 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:00,348 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-15.json
2025-07-07 16:59:00,348 - INFO - 下载内幕交易数据: MSFT (2025-01-15)
2025-07-07 16:59:00,454 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:00,612 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:00,851 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-15.json
2025-07-07 16:59:00,852 - INFO - 下载公司新闻数据: MSFT (2025-01-15)
2025-07-07 16:59:01,359 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-15.json
2025-07-07 16:59:01,360 - INFO - 下载市值数据: MSFT (2025-01-15)
2025-07-07 16:59:01,861 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-15.json
2025-07-07 16:59:01,862 - INFO - 下载财务科目数据: MSFT (2025-01-15)
2025-07-07 16:59:02,501 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:02,623 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:02,624 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:02,624 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:03,175 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:03,176 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:03,177 - INFO - 下载财务指标数据: NVDA (2025-01-22)
2025-07-07 16:59:03,680 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-22.json
2025-07-07 16:59:03,681 - INFO - 下载内幕交易数据: NVDA (2025-01-22)
2025-07-07 16:59:04,184 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-22.json
2025-07-07 16:59:04,185 - INFO - 下载公司新闻数据: NVDA (2025-01-22)
2025-07-07 16:59:04,265 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:04,265 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:04,266 - INFO - 下载财务指标数据: MSFT (2025-01-22)
2025-07-07 16:59:04,692 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-22.json
2025-07-07 16:59:04,693 - INFO - 下载市值数据: NVDA (2025-01-22)
2025-07-07 16:59:04,769 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-22.json
2025-07-07 16:59:04,770 - INFO - 下载内幕交易数据: MSFT (2025-01-22)
2025-07-07 16:59:05,195 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-22.json
2025-07-07 16:59:05,195 - INFO - 下载财务科目数据: NVDA (2025-01-22)
2025-07-07 16:59:05,275 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-22.json
2025-07-07 16:59:05,276 - INFO - 下载公司新闻数据: MSFT (2025-01-22)
2025-07-07 16:59:05,779 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-22.json
2025-07-07 16:59:05,779 - INFO - 下载市值数据: MSFT (2025-01-22)
2025-07-07 16:59:05,835 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:06,136 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:06,136 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:06,136 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:06,204 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:06,281 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-22.json
2025-07-07 16:59:06,281 - INFO - 下载财务科目数据: MSFT (2025-01-22)
2025-07-07 16:59:06,934 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:07,001 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:07,001 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:07,002 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:07,084 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:10,205 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:10,206 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:59:10,332 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:11,086 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:11,086 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:59:11,142 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:16,455 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:16,456 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 16:59:16,538 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:18,332 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:18,332 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:19,144 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:19,144 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:19,207 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:19,261 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:19,261 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:19,261 - INFO - 下载财务指标数据: NVDA (2025-01-29)
2025-07-07 16:59:19,766 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-01-29.json
2025-07-07 16:59:19,766 - INFO - 下载内幕交易数据: NVDA (2025-01-29)
2025-07-07 16:59:20,271 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-01-29.json
2025-07-07 16:59:20,272 - INFO - 下载公司新闻数据: NVDA (2025-01-29)
2025-07-07 16:59:20,777 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-01-29.json
2025-07-07 16:59:20,778 - INFO - 下载市值数据: NVDA (2025-01-29)
2025-07-07 16:59:21,281 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-01-29.json
2025-07-07 16:59:21,282 - INFO - 下载财务科目数据: NVDA (2025-01-29)
2025-07-07 16:59:21,927 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:22,811 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:22,812 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:22,812 - INFO - 下载财务指标数据: NVDA (2025-02-05)
2025-07-07 16:59:23,316 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-05.json
2025-07-07 16:59:23,316 - INFO - 下载内幕交易数据: NVDA (2025-02-05)
2025-07-07 16:59:23,820 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-05.json
2025-07-07 16:59:23,820 - INFO - 下载公司新闻数据: NVDA (2025-02-05)
2025-07-07 16:59:24,326 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-05.json
2025-07-07 16:59:24,326 - INFO - 下载市值数据: NVDA (2025-02-05)
2025-07-07 16:59:24,828 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-05.json
2025-07-07 16:59:24,829 - INFO - 下载财务科目数据: NVDA (2025-02-05)
2025-07-07 16:59:25,460 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:26,387 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:26,388 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:26,391 - INFO - 下载财务指标数据: NVDA (2025-02-12)
2025-07-07 16:59:26,893 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-12.json
2025-07-07 16:59:26,894 - INFO - 下载内幕交易数据: NVDA (2025-02-12)
2025-07-07 16:59:27,396 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-12.json
2025-07-07 16:59:27,396 - INFO - 下载公司新闻数据: NVDA (2025-02-12)
2025-07-07 16:59:27,902 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-12.json
2025-07-07 16:59:27,902 - INFO - 下载市值数据: NVDA (2025-02-12)
2025-07-07 16:59:28,405 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-12.json
2025-07-07 16:59:28,406 - INFO - 下载财务科目数据: NVDA (2025-02-12)
2025-07-07 16:59:29,041 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:29,131 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:29,131 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:29,132 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:29,152 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:33,153 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:33,154 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 16:59:33,176 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:35,208 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:35,209 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 16:59:35,234 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:41,177 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:41,177 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 16:59:41,266 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:48,539 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:48,540 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 16:59:49,442 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 16:59:49,443 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:59:49,444 - INFO - 下载财务指标数据: AAPL (2025-01-22)
2025-07-07 16:59:50,077 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:50,163 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-22&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:50,163 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-22&limit=10&period=ttm
2025-07-07 16:59:50,163 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:51,126 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-22&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 16:59:51,340 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-22.json
2025-07-07 16:59:51,341 - INFO - 下载内幕交易数据: AAPL (2025-01-22)
2025-07-07 16:59:52,012 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:52,501 - DEBUG - Incremented Retry for (url='/insider-trades/?ticker=AAPL&filing_date_lte=2025-01-22&limit=50'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:52,501 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-22&limit=50
2025-07-07 16:59:52,501 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:54,037 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-22&limit=50 HTTP/1.1" 200 None
2025-07-07 16:59:54,331 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-22.json
2025-07-07 16:59:54,332 - INFO - 下载公司新闻数据: AAPL (2025-01-22)
2025-07-07 16:59:54,968 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:56,005 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=AAPL&end_date=2025-01-22&limit=100 HTTP/1.1" 200 None
2025-07-07 16:59:56,435 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-22.json
2025-07-07 16:59:56,435 - INFO - 下载市值数据: AAPL (2025-01-22)
2025-07-07 16:59:56,936 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-22.json
2025-07-07 16:59:56,937 - INFO - 下载财务科目数据: AAPL (2025-01-22)
2025-07-07 16:59:57,267 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:57,267 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 16:59:57,375 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:57,572 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 16:59:57,660 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 16:59:57,660 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:59:57,661 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 16:59:58,470 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:02,471 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:02,471 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:03,712 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:03,713 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:03,713 - INFO - 下载财务指标数据: AAPL (2025-01-29)
2025-07-07 17:00:04,216 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-29.json
2025-07-07 17:00:04,216 - INFO - 下载内幕交易数据: AAPL (2025-01-29)
2025-07-07 17:00:04,720 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-29.json
2025-07-07 17:00:04,722 - INFO - 下载公司新闻数据: AAPL (2025-01-29)
2025-07-07 17:00:05,227 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-29.json
2025-07-07 17:00:05,228 - INFO - 下载市值数据: AAPL (2025-01-29)
2025-07-07 17:00:05,732 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-29.json
2025-07-07 17:00:05,732 - INFO - 下载财务科目数据: AAPL (2025-01-29)
2025-07-07 17:00:06,368 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:07,235 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:07,235 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:00:07,300 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:07,301 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:07,302 - INFO - 下载财务指标数据: AAPL (2025-02-05)
2025-07-07 17:00:07,803 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-05.json
2025-07-07 17:00:07,803 - INFO - 下载内幕交易数据: AAPL (2025-02-05)
2025-07-07 17:00:08,307 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-05.json
2025-07-07 17:00:08,308 - INFO - 下载公司新闻数据: AAPL (2025-02-05)
2025-07-07 17:00:08,318 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:00:08,811 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-05.json
2025-07-07 17:00:08,812 - INFO - 下载市值数据: AAPL (2025-02-05)
2025-07-07 17:00:09,258 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:09,259 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:09,260 - INFO - 下载财务指标数据: MSFT (2025-01-29)
2025-07-07 17:00:09,312 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-05.json
2025-07-07 17:00:09,312 - INFO - 下载财务科目数据: AAPL (2025-02-05)
2025-07-07 17:00:09,764 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-01-29.json
2025-07-07 17:00:09,764 - INFO - 下载内幕交易数据: MSFT (2025-01-29)
2025-07-07 17:00:09,946 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:10,094 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:10,094 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:10,096 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:10,167 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:10,269 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-01-29.json
2025-07-07 17:00:10,270 - INFO - 下载公司新闻数据: MSFT (2025-01-29)
2025-07-07 17:00:10,776 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-01-29.json
2025-07-07 17:00:10,777 - INFO - 下载市值数据: MSFT (2025-01-29)
2025-07-07 17:00:11,279 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-01-29.json
2025-07-07 17:00:11,280 - INFO - 下载财务科目数据: MSFT (2025-01-29)
2025-07-07 17:00:11,920 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:12,235 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:12,236 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:12,236 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:12,298 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:14,168 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:14,168 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:15,170 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:15,170 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:15,170 - INFO - 下载财务指标数据: AAPL (2025-02-12)
2025-07-07 17:00:15,674 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-12.json
2025-07-07 17:00:15,675 - INFO - 下载内幕交易数据: AAPL (2025-02-12)
2025-07-07 17:00:16,176 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-12.json
2025-07-07 17:00:16,177 - INFO - 下载公司新闻数据: AAPL (2025-02-12)
2025-07-07 17:00:16,299 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:16,299 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:16,683 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-12.json
2025-07-07 17:00:16,684 - INFO - 下载市值数据: AAPL (2025-02-12)
2025-07-07 17:00:17,185 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-12.json
2025-07-07 17:00:17,186 - INFO - 下载财务科目数据: AAPL (2025-02-12)
2025-07-07 17:00:17,325 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:17,325 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:17,326 - INFO - 下载财务指标数据: MSFT (2025-02-05)
2025-07-07 17:00:17,828 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-05.json
2025-07-07 17:00:17,831 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:17,831 - INFO - 下载内幕交易数据: MSFT (2025-02-05)
2025-07-07 17:00:18,333 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-05.json
2025-07-07 17:00:18,334 - INFO - 下载公司新闻数据: MSFT (2025-02-05)
2025-07-07 17:00:18,837 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-05.json
2025-07-07 17:00:18,838 - INFO - 下载市值数据: MSFT (2025-02-05)
2025-07-07 17:00:19,195 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:19,195 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:19,196 - INFO - 下载财务指标数据: AAPL (2025-02-19)
2025-07-07 17:00:19,340 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-05.json
2025-07-07 17:00:19,341 - INFO - 下载财务科目数据: MSFT (2025-02-05)
2025-07-07 17:00:19,697 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-19.json
2025-07-07 17:00:19,698 - INFO - 下载内幕交易数据: AAPL (2025-02-19)
2025-07-07 17:00:19,996 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:20,030 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:20,030 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:20,030 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:20,061 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:20,200 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-19.json
2025-07-07 17:00:20,200 - INFO - 下载公司新闻数据: AAPL (2025-02-19)
2025-07-07 17:00:20,707 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-19.json
2025-07-07 17:00:20,708 - INFO - 下载市值数据: AAPL (2025-02-19)
2025-07-07 17:00:21,209 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-19.json
2025-07-07 17:00:21,209 - INFO - 下载财务科目数据: AAPL (2025-02-19)
2025-07-07 17:00:21,845 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:21,924 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:21,925 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:21,926 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:22,241 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:24,062 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:24,063 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:24,185 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:26,242 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:26,242 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:26,315 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:29,376 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:29,376 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:00:30,254 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:30,255 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:30,257 - INFO - 下载财务指标数据: NVDA (2025-02-19)
2025-07-07 17:00:30,759 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-19.json
2025-07-07 17:00:30,760 - INFO - 下载内幕交易数据: NVDA (2025-02-19)
2025-07-07 17:00:31,262 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-19.json
2025-07-07 17:00:31,262 - INFO - 下载公司新闻数据: NVDA (2025-02-19)
2025-07-07 17:00:31,766 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-19.json
2025-07-07 17:00:31,767 - INFO - 下载市值数据: NVDA (2025-02-19)
2025-07-07 17:00:32,186 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:32,186 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:32,269 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-19.json
2025-07-07 17:00:32,269 - INFO - 下载财务科目数据: NVDA (2025-02-19)
2025-07-07 17:00:32,904 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:33,100 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:33,100 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:33,101 - INFO - 下载财务指标数据: MSFT (2025-02-12)
2025-07-07 17:00:33,603 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-12.json
2025-07-07 17:00:33,603 - INFO - 下载内幕交易数据: MSFT (2025-02-12)
2025-07-07 17:00:33,855 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:33,855 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:33,857 - INFO - 下载财务指标数据: NVDA (2025-02-26)
2025-07-07 17:00:34,107 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-12.json
2025-07-07 17:00:34,108 - INFO - 下载公司新闻数据: MSFT (2025-02-12)
2025-07-07 17:00:34,316 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:34,316 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:34,359 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-02-26.json
2025-07-07 17:00:34,359 - INFO - 下载内幕交易数据: NVDA (2025-02-26)
2025-07-07 17:00:34,374 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:34,612 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-12.json
2025-07-07 17:00:34,614 - INFO - 下载市值数据: MSFT (2025-02-12)
2025-07-07 17:00:34,861 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-02-26.json
2025-07-07 17:00:34,861 - INFO - 下载公司新闻数据: NVDA (2025-02-26)
2025-07-07 17:00:35,115 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-12.json
2025-07-07 17:00:35,115 - INFO - 下载财务科目数据: MSFT (2025-02-12)
2025-07-07 17:00:35,366 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-02-26.json
2025-07-07 17:00:35,367 - INFO - 下载市值数据: NVDA (2025-02-26)
2025-07-07 17:00:35,750 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:35,811 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:35,811 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:35,811 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:35,862 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:35,869 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-02-26.json
2025-07-07 17:00:35,869 - INFO - 下载财务科目数据: NVDA (2025-02-26)
2025-07-07 17:00:36,501 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:36,545 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:36,545 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:36,545 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:36,584 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:39,862 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:39,863 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:39,906 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:40,585 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:40,585 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:40,622 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:47,908 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:47,909 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:47,949 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:48,624 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:48,624 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:00:49,596 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:49,597 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:49,597 - INFO - 下载财务指标数据: NVDA (2025-03-05)
2025-07-07 17:00:50,099 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-05.json
2025-07-07 17:00:50,099 - INFO - 下载内幕交易数据: NVDA (2025-03-05)
2025-07-07 17:00:50,375 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:50,375 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:00:50,604 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-05.json
2025-07-07 17:00:50,604 - INFO - 下载公司新闻数据: NVDA (2025-03-05)
2025-07-07 17:00:51,108 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-05.json
2025-07-07 17:00:51,109 - INFO - 下载市值数据: NVDA (2025-03-05)
2025-07-07 17:00:51,240 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:00:51,240 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:00:51,241 - INFO - 下载财务指标数据: AAPL (2025-02-26)
2025-07-07 17:00:51,611 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-05.json
2025-07-07 17:00:51,611 - INFO - 下载财务科目数据: NVDA (2025-03-05)
2025-07-07 17:00:51,742 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-02-26.json
2025-07-07 17:00:51,742 - INFO - 下载内幕交易数据: AAPL (2025-02-26)
2025-07-07 17:00:52,241 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:52,244 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-02-26.json
2025-07-07 17:00:52,244 - INFO - 下载公司新闻数据: AAPL (2025-02-26)
2025-07-07 17:00:52,287 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:52,287 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:52,287 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:52,327 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:52,746 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-02-26.json
2025-07-07 17:00:52,746 - INFO - 下载市值数据: AAPL (2025-02-26)
2025-07-07 17:00:53,248 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-02-26.json
2025-07-07 17:00:53,249 - INFO - 下载财务科目数据: AAPL (2025-02-26)
2025-07-07 17:00:53,876 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:00:53,916 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:53,917 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:53,917 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:00:53,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:56,328 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:56,328 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:56,379 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:00:57,967 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:00:57,968 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:00:58,010 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:03,950 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:03,951 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:01:04,011 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:04,380 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:04,380 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:04,425 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:06,011 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:06,011 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:06,094 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:20,427 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:20,427 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:01:20,463 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:22,095 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:22,095 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:01:24,149 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:24,150 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:24,151 - INFO - 下载财务指标数据: AAPL (2025-03-05)
2025-07-07 17:01:24,654 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-05.json
2025-07-07 17:01:24,655 - INFO - 下载内幕交易数据: AAPL (2025-03-05)
2025-07-07 17:01:25,158 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-05.json
2025-07-07 17:01:25,159 - INFO - 下载公司新闻数据: AAPL (2025-03-05)
2025-07-07 17:01:25,661 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-05.json
2025-07-07 17:01:25,662 - INFO - 下载市值数据: AAPL (2025-03-05)
2025-07-07 17:01:26,164 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-05.json
2025-07-07 17:01:26,165 - INFO - 下载财务科目数据: AAPL (2025-03-05)
2025-07-07 17:01:26,800 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:26,888 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:26,888 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:26,889 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:27,002 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:31,002 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:31,004 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:01:31,101 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:36,012 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:36,013 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:01:37,122 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:01:37,208 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:37,209 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:37,210 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:01:38,138 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:38,139 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:38,140 - INFO - 下载财务指标数据: MSFT (2025-02-19)
2025-07-07 17:01:38,641 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-19.json
2025-07-07 17:01:38,642 - INFO - 下载内幕交易数据: MSFT (2025-02-19)
2025-07-07 17:01:39,103 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:39,103 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:39,143 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-19.json
2025-07-07 17:01:39,144 - INFO - 下载公司新闻数据: MSFT (2025-02-19)
2025-07-07 17:01:39,648 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-19.json
2025-07-07 17:01:39,649 - INFO - 下载市值数据: MSFT (2025-02-19)
2025-07-07 17:01:40,028 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:40,029 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:40,030 - INFO - 下载财务指标数据: AAPL (2025-03-12)
2025-07-07 17:01:40,151 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-19.json
2025-07-07 17:01:40,152 - INFO - 下载财务科目数据: MSFT (2025-02-19)
2025-07-07 17:01:40,532 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-12.json
2025-07-07 17:01:40,533 - INFO - 下载内幕交易数据: AAPL (2025-03-12)
2025-07-07 17:01:40,790 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:40,845 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:40,845 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:40,845 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:40,900 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:41,036 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-12.json
2025-07-07 17:01:41,037 - INFO - 下载公司新闻数据: AAPL (2025-03-12)
2025-07-07 17:01:41,542 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-12.json
2025-07-07 17:01:41,542 - INFO - 下载市值数据: AAPL (2025-03-12)
2025-07-07 17:01:42,045 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-12.json
2025-07-07 17:01:42,046 - INFO - 下载财务科目数据: AAPL (2025-03-12)
2025-07-07 17:01:42,684 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:42,796 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:42,796 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:42,796 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:42,919 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:44,901 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:44,901 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:01:44,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:46,920 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:46,920 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:01:47,004 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:52,464 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:52,465 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:01:52,967 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:52,968 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:53,069 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:53,453 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:53,454 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:53,454 - INFO - 下载财务指标数据: NVDA (2025-03-12)
2025-07-07 17:01:53,956 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-12.json
2025-07-07 17:01:53,956 - INFO - 下载内幕交易数据: NVDA (2025-03-12)
2025-07-07 17:01:54,458 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-12.json
2025-07-07 17:01:54,459 - INFO - 下载公司新闻数据: NVDA (2025-03-12)
2025-07-07 17:01:54,961 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-12.json
2025-07-07 17:01:54,962 - INFO - 下载市值数据: NVDA (2025-03-12)
2025-07-07 17:01:55,006 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:55,007 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:01:55,108 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:55,463 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-12.json
2025-07-07 17:01:55,463 - INFO - 下载财务科目数据: NVDA (2025-03-12)
2025-07-07 17:01:56,097 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:57,004 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:01:57,006 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:01:57,007 - INFO - 下载财务指标数据: NVDA (2025-03-19)
2025-07-07 17:01:57,509 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-19.json
2025-07-07 17:01:57,509 - INFO - 下载内幕交易数据: NVDA (2025-03-19)
2025-07-07 17:01:58,012 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-19.json
2025-07-07 17:01:58,012 - INFO - 下载公司新闻数据: NVDA (2025-03-19)
2025-07-07 17:01:58,515 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-19.json
2025-07-07 17:01:58,516 - INFO - 下载市值数据: NVDA (2025-03-19)
2025-07-07 17:01:59,017 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-19.json
2025-07-07 17:01:59,018 - INFO - 下载财务科目数据: NVDA (2025-03-19)
2025-07-07 17:01:59,652 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:01:59,729 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:01:59,729 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:01:59,730 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:01:59,775 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:03,777 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:03,777 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:02:03,819 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:09,070 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:09,070 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:10,024 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:02:10,025 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:02:10,026 - INFO - 下载财务指标数据: MSFT (2025-02-26)
2025-07-07 17:02:10,528 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-02-26.json
2025-07-07 17:02:10,529 - INFO - 下载内幕交易数据: MSFT (2025-02-26)
2025-07-07 17:02:11,032 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-02-26.json
2025-07-07 17:02:11,033 - INFO - 下载公司新闻数据: MSFT (2025-02-26)
2025-07-07 17:02:11,110 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:11,111 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:11,535 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-02-26.json
2025-07-07 17:02:11,535 - INFO - 下载市值数据: MSFT (2025-02-26)
2025-07-07 17:02:11,819 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:11,819 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:02:11,932 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:12,038 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-02-26.json
2025-07-07 17:02:12,039 - INFO - 下载财务科目数据: MSFT (2025-02-26)
2025-07-07 17:02:12,215 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:12,679 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:02:13,633 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:02:13,633 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:02:13,634 - INFO - 下载财务指标数据: MSFT (2025-03-05)
2025-07-07 17:02:14,136 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-05.json
2025-07-07 17:02:14,137 - INFO - 下载内幕交易数据: MSFT (2025-03-05)
2025-07-07 17:02:14,641 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-05.json
2025-07-07 17:02:14,643 - INFO - 下载公司新闻数据: MSFT (2025-03-05)
2025-07-07 17:02:15,147 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-05.json
2025-07-07 17:02:15,148 - INFO - 下载市值数据: MSFT (2025-03-05)
2025-07-07 17:02:15,650 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-05.json
2025-07-07 17:02:15,650 - INFO - 下载财务科目数据: MSFT (2025-03-05)
2025-07-07 17:02:16,287 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:02:16,378 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:16,378 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:16,379 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:02:16,458 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:20,460 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:20,460 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:02:21,584 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:27,934 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:27,934 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:27,997 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:29,584 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:29,585 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:02:29,624 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:44,216 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:44,217 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:02:45,377 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:02:45,465 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:45,465 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:45,465 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:02:45,557 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:45,626 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:45,626 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:02:45,710 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:49,558 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:49,558 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:02:49,596 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:57,597 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:57,597 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:02:57,774 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:02:59,998 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:02:59,998 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:03:00,971 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:00,972 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:00,974 - INFO - 下载财务指标数据: NVDA (2025-03-26)
2025-07-07 17:03:01,475 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-03-26.json
2025-07-07 17:03:01,476 - INFO - 下载内幕交易数据: NVDA (2025-03-26)
2025-07-07 17:03:01,978 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-03-26.json
2025-07-07 17:03:01,979 - INFO - 下载公司新闻数据: NVDA (2025-03-26)
2025-07-07 17:03:02,483 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-03-26.json
2025-07-07 17:03:02,483 - INFO - 下载市值数据: NVDA (2025-03-26)
2025-07-07 17:03:02,986 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-03-26.json
2025-07-07 17:03:02,986 - INFO - 下载财务科目数据: NVDA (2025-03-26)
2025-07-07 17:03:03,623 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:04,501 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:04,502 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:04,503 - INFO - 下载财务指标数据: NVDA (2025-04-02)
2025-07-07 17:03:05,006 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-02.json
2025-07-07 17:03:05,007 - INFO - 下载内幕交易数据: NVDA (2025-04-02)
2025-07-07 17:03:05,510 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-02.json
2025-07-07 17:03:05,511 - INFO - 下载公司新闻数据: NVDA (2025-04-02)
2025-07-07 17:03:06,015 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-02.json
2025-07-07 17:03:06,016 - INFO - 下载市值数据: NVDA (2025-04-02)
2025-07-07 17:03:06,517 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-02.json
2025-07-07 17:03:06,517 - INFO - 下载财务科目数据: NVDA (2025-04-02)
2025-07-07 17:03:07,161 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:07,253 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:07,253 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:07,254 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:07,325 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:11,327 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:11,327 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:11,368 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:13,775 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:13,776 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:03:14,720 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:14,720 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:14,721 - INFO - 下载财务指标数据: AAPL (2025-03-19)
2025-07-07 17:03:15,224 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-19.json
2025-07-07 17:03:15,225 - INFO - 下载内幕交易数据: AAPL (2025-03-19)
2025-07-07 17:03:15,728 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-19.json
2025-07-07 17:03:15,729 - INFO - 下载公司新闻数据: AAPL (2025-03-19)
2025-07-07 17:03:16,231 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-19.json
2025-07-07 17:03:16,231 - INFO - 下载市值数据: AAPL (2025-03-19)
2025-07-07 17:03:16,732 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-19.json
2025-07-07 17:03:16,732 - INFO - 下载财务科目数据: AAPL (2025-03-19)
2025-07-07 17:03:17,375 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:17,476 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:17,476 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:17,476 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:17,711 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:17,711 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:03:18,440 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:18,441 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:18,442 - INFO - 下载财务指标数据: AAPL (2025-03-26)
2025-07-07 17:03:18,778 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:03:18,871 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:18,871 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:18,872 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:03:18,943 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-03-26.json
2025-07-07 17:03:18,944 - INFO - 下载内幕交易数据: AAPL (2025-03-26)
2025-07-07 17:03:18,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:19,369 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:19,371 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:19,447 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-03-26.json
2025-07-07 17:03:19,447 - INFO - 下载公司新闻数据: AAPL (2025-03-26)
2025-07-07 17:03:19,463 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:19,950 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-03-26.json
2025-07-07 17:03:19,951 - INFO - 下载市值数据: AAPL (2025-03-26)
2025-07-07 17:03:20,454 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-03-26.json
2025-07-07 17:03:20,454 - INFO - 下载财务科目数据: AAPL (2025-03-26)
2025-07-07 17:03:21,086 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:21,189 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:21,191 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:21,192 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:22,142 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:22,142 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:22,143 - INFO - 下载财务指标数据: AAPL (2025-04-02)
2025-07-07 17:03:22,645 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-02.json
2025-07-07 17:03:22,646 - INFO - 下载内幕交易数据: AAPL (2025-04-02)
2025-07-07 17:03:22,967 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:22,967 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:03:23,027 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:23,147 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-02.json
2025-07-07 17:03:23,147 - INFO - 下载公司新闻数据: AAPL (2025-04-02)
2025-07-07 17:03:23,652 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-02.json
2025-07-07 17:03:23,652 - INFO - 下载市值数据: AAPL (2025-04-02)
2025-07-07 17:03:24,154 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-02.json
2025-07-07 17:03:24,157 - INFO - 下载财务科目数据: AAPL (2025-04-02)
2025-07-07 17:03:24,785 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:24,844 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:24,845 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:24,845 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:24,935 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:28,936 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:28,937 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:29,027 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:31,029 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:31,029 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:03:31,866 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:31,867 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:31,867 - INFO - 下载财务指标数据: MSFT (2025-03-12)
2025-07-07 17:03:32,368 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-12.json
2025-07-07 17:03:32,369 - INFO - 下载内幕交易数据: MSFT (2025-03-12)
2025-07-07 17:03:32,874 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-12.json
2025-07-07 17:03:32,874 - INFO - 下载公司新闻数据: MSFT (2025-03-12)
2025-07-07 17:03:33,378 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-12.json
2025-07-07 17:03:33,379 - INFO - 下载市值数据: MSFT (2025-03-12)
2025-07-07 17:03:33,881 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-12.json
2025-07-07 17:03:33,881 - INFO - 下载财务科目数据: MSFT (2025-03-12)
2025-07-07 17:03:34,516 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:34,566 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:34,566 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:34,566 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:34,640 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:35,464 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:35,465 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:03:35,509 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:37,028 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:37,029 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:37,888 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:37,888 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:37,889 - INFO - 下载财务指标数据: AAPL (2025-04-09)
2025-07-07 17:03:38,391 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-09.json
2025-07-07 17:03:38,392 - INFO - 下载内幕交易数据: AAPL (2025-04-09)
2025-07-07 17:03:38,642 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:38,642 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:38,682 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:38,895 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-09.json
2025-07-07 17:03:38,896 - INFO - 下载公司新闻数据: AAPL (2025-04-09)
2025-07-07 17:03:39,400 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-09.json
2025-07-07 17:03:39,401 - INFO - 下载市值数据: AAPL (2025-04-09)
2025-07-07 17:03:39,902 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-09.json
2025-07-07 17:03:39,902 - INFO - 下载财务科目数据: AAPL (2025-04-09)
2025-07-07 17:03:40,534 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:40,574 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:40,575 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:40,575 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:40,617 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:44,618 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:44,619 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:44,671 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:46,683 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:46,684 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:47,557 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:03:47,558 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:03:47,558 - INFO - 下载财务指标数据: MSFT (2025-03-19)
2025-07-07 17:03:48,061 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-19.json
2025-07-07 17:03:48,062 - INFO - 下载内幕交易数据: MSFT (2025-03-19)
2025-07-07 17:03:48,566 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-19.json
2025-07-07 17:03:48,567 - INFO - 下载公司新闻数据: MSFT (2025-03-19)
2025-07-07 17:03:49,071 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-19.json
2025-07-07 17:03:49,072 - INFO - 下载市值数据: MSFT (2025-03-19)
2025-07-07 17:03:49,574 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-19.json
2025-07-07 17:03:49,576 - INFO - 下载财务科目数据: MSFT (2025-03-19)
2025-07-07 17:03:50,212 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:03:50,253 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:50,253 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:50,253 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:03:50,295 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:52,672 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:52,673 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:03:52,710 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:03:54,296 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:03:54,296 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:03:54,335 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:02,336 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:02,336 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:04:02,384 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:07,510 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:07,510 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:04:08,550 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:04:08,710 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:08,710 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:04:08,744 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:09,500 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:04:09,500 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:04:09,500 - INFO - 下载财务指标数据: NVDA (2025-04-09)
2025-07-07 17:04:10,003 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-09.json
2025-07-07 17:04:10,003 - INFO - 下载内幕交易数据: NVDA (2025-04-09)
2025-07-07 17:04:10,507 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-09.json
2025-07-07 17:04:10,508 - INFO - 下载公司新闻数据: NVDA (2025-04-09)
2025-07-07 17:04:11,012 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-09.json
2025-07-07 17:04:11,013 - INFO - 下载市值数据: NVDA (2025-04-09)
2025-07-07 17:04:11,515 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-09.json
2025-07-07 17:04:11,516 - INFO - 下载财务科目数据: NVDA (2025-04-09)
2025-07-07 17:04:12,159 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:04:12,200 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:12,201 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:12,201 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:04:12,245 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:16,246 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:16,247 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:04:16,319 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:18,385 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:18,386 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:04:18,425 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:24,320 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:24,320 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:04:24,354 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:40,355 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:40,356 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:04:40,398 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:40,745 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:40,745 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:04:41,785 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:04:41,871 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:41,872 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:41,873 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:04:41,958 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:45,960 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:45,960 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:04:46,000 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:50,426 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:50,426 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:04:51,478 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:04:51,512 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:51,512 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:51,514 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:04:51,550 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:54,001 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:54,002 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:04:54,039 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:04:55,550 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:04:55,551 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:04:55,588 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:03,589 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:03,590 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:05:03,628 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:10,040 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:10,041 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:05:10,081 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:12,399 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:12,399 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:05:13,444 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:05:13,484 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:13,484 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:13,485 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:05:14,374 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:05:14,375 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:05:14,376 - INFO - 下载财务指标数据: NVDA (2025-04-16)
2025-07-07 17:05:14,878 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-16.json
2025-07-07 17:05:14,878 - INFO - 下载内幕交易数据: NVDA (2025-04-16)
2025-07-07 17:05:15,381 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-16.json
2025-07-07 17:05:15,383 - INFO - 下载公司新闻数据: NVDA (2025-04-16)
2025-07-07 17:05:15,891 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-16.json
2025-07-07 17:05:15,892 - INFO - 下载市值数据: NVDA (2025-04-16)
2025-07-07 17:05:16,394 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-16.json
2025-07-07 17:05:16,394 - INFO - 下载财务科目数据: NVDA (2025-04-16)
2025-07-07 17:05:17,032 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:05:17,086 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:17,086 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:17,087 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:05:17,146 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:19,629 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:19,629 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:05:19,669 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:21,146 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:21,146 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:05:22,025 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:05:22,025 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:05:22,026 - INFO - 下载财务指标数据: NVDA (2025-04-23)
2025-07-07 17:05:22,528 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-23.json
2025-07-07 17:05:22,529 - INFO - 下载内幕交易数据: NVDA (2025-04-23)
2025-07-07 17:05:23,031 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-23.json
2025-07-07 17:05:23,031 - INFO - 下载公司新闻数据: NVDA (2025-04-23)
2025-07-07 17:05:23,535 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-23.json
2025-07-07 17:05:23,536 - INFO - 下载市值数据: NVDA (2025-04-23)
2025-07-07 17:05:24,038 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-23.json
2025-07-07 17:05:24,040 - INFO - 下载财务科目数据: NVDA (2025-04-23)
2025-07-07 17:05:24,676 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:05:24,721 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:24,721 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:24,721 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:05:24,767 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:28,768 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:28,768 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:05:28,804 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:36,806 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:36,807 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:05:36,849 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:42,082 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:42,082 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:05:44,120 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:05:44,164 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:44,164 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:44,164 - DEBUG - Starting new HTTPS connection (14): api.financialdatasets.ai:443
2025-07-07 17:05:44,206 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:48,208 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:48,208 - DEBUG - Starting new HTTPS connection (15): api.financialdatasets.ai:443
2025-07-07 17:05:48,251 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:51,669 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:51,670 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:05:52,849 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:52,849 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:05:52,893 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:53,712 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:05:53,750 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:53,751 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:53,751 - DEBUG - Starting new HTTPS connection (14): api.financialdatasets.ai:443
2025-07-07 17:05:53,790 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:56,251 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:56,251 - DEBUG - Starting new HTTPS connection (16): api.financialdatasets.ai:443
2025-07-07 17:05:57,140 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:05:57,140 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:05:57,140 - INFO - 下载财务指标数据: AAPL (2025-04-16)
2025-07-07 17:05:57,643 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-16.json
2025-07-07 17:05:57,644 - INFO - 下载内幕交易数据: AAPL (2025-04-16)
2025-07-07 17:05:57,792 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:57,792 - DEBUG - Starting new HTTPS connection (15): api.financialdatasets.ai:443
2025-07-07 17:05:57,894 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:58,147 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-16.json
2025-07-07 17:05:58,148 - INFO - 下载公司新闻数据: AAPL (2025-04-16)
2025-07-07 17:05:58,650 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-16.json
2025-07-07 17:05:58,651 - INFO - 下载市值数据: AAPL (2025-04-16)
2025-07-07 17:05:59,153 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-16.json
2025-07-07 17:05:59,153 - INFO - 下载财务科目数据: AAPL (2025-04-16)
2025-07-07 17:05:59,789 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:05:59,885 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:05:59,885 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:05:59,885 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:00,808 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:00,809 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:00,809 - INFO - 下载财务指标数据: AAPL (2025-04-23)
2025-07-07 17:06:01,311 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-23.json
2025-07-07 17:06:01,312 - INFO - 下载内幕交易数据: AAPL (2025-04-23)
2025-07-07 17:06:01,814 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-23.json
2025-07-07 17:06:01,814 - INFO - 下载公司新闻数据: AAPL (2025-04-23)
2025-07-07 17:06:02,318 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-23.json
2025-07-07 17:06:02,319 - INFO - 下载市值数据: AAPL (2025-04-23)
2025-07-07 17:06:02,822 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-23.json
2025-07-07 17:06:02,823 - INFO - 下载财务科目数据: AAPL (2025-04-23)
2025-07-07 17:06:03,458 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:03,488 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:03,488 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:03,489 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:03,524 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:05,894 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:05,894 - DEBUG - Starting new HTTPS connection (16): api.financialdatasets.ai:443
2025-07-07 17:06:05,930 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:07,525 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:07,525 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:06:07,623 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:15,625 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:15,625 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:06:16,561 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:16,561 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:16,562 - INFO - 下载财务指标数据: AAPL (2025-04-30)
2025-07-07 17:06:17,065 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-04-30.json
2025-07-07 17:06:17,066 - INFO - 下载内幕交易数据: AAPL (2025-04-30)
2025-07-07 17:06:17,569 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-04-30.json
2025-07-07 17:06:17,570 - INFO - 下载公司新闻数据: AAPL (2025-04-30)
2025-07-07 17:06:18,075 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-04-30.json
2025-07-07 17:06:18,076 - INFO - 下载市值数据: AAPL (2025-04-30)
2025-07-07 17:06:18,577 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-04-30.json
2025-07-07 17:06:18,578 - INFO - 下载财务科目数据: AAPL (2025-04-30)
2025-07-07 17:06:19,209 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:20,111 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:20,112 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:20,113 - INFO - 下载财务指标数据: AAPL (2025-05-07)
2025-07-07 17:06:20,616 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-07.json
2025-07-07 17:06:20,617 - INFO - 下载内幕交易数据: AAPL (2025-05-07)
2025-07-07 17:06:21,119 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-07.json
2025-07-07 17:06:21,119 - INFO - 下载公司新闻数据: AAPL (2025-05-07)
2025-07-07 17:06:21,622 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-07.json
2025-07-07 17:06:21,623 - INFO - 下载市值数据: AAPL (2025-05-07)
2025-07-07 17:06:21,931 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:21,931 - DEBUG - Starting new HTTPS connection (17): api.financialdatasets.ai:443
2025-07-07 17:06:21,972 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:22,125 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-07.json
2025-07-07 17:06:22,125 - INFO - 下载财务科目数据: AAPL (2025-05-07)
2025-07-07 17:06:22,763 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:22,794 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:22,794 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:22,794 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:22,832 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:24,894 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:24,895 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:06:25,750 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:25,750 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:25,751 - INFO - 下载财务指标数据: NVDA (2025-04-30)
2025-07-07 17:06:26,253 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-04-30.json
2025-07-07 17:06:26,253 - INFO - 下载内幕交易数据: NVDA (2025-04-30)
2025-07-07 17:06:26,757 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-04-30.json
2025-07-07 17:06:26,758 - INFO - 下载公司新闻数据: NVDA (2025-04-30)
2025-07-07 17:06:26,832 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:26,832 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:06:26,868 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:27,261 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-04-30.json
2025-07-07 17:06:27,262 - INFO - 下载市值数据: NVDA (2025-04-30)
2025-07-07 17:06:27,764 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-04-30.json
2025-07-07 17:06:27,765 - INFO - 下载财务科目数据: NVDA (2025-04-30)
2025-07-07 17:06:28,402 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:29,271 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:29,272 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:29,273 - INFO - 下载财务指标数据: NVDA (2025-05-07)
2025-07-07 17:06:29,776 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-07.json
2025-07-07 17:06:29,777 - INFO - 下载内幕交易数据: NVDA (2025-05-07)
2025-07-07 17:06:30,280 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-07.json
2025-07-07 17:06:30,280 - INFO - 下载公司新闻数据: NVDA (2025-05-07)
2025-07-07 17:06:30,784 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-07.json
2025-07-07 17:06:30,784 - INFO - 下载市值数据: NVDA (2025-05-07)
2025-07-07 17:06:31,286 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-07.json
2025-07-07 17:06:31,286 - INFO - 下载财务科目数据: NVDA (2025-05-07)
2025-07-07 17:06:31,926 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:31,966 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:31,967 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:31,968 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:32,819 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:32,819 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:32,819 - INFO - 下载财务指标数据: NVDA (2025-05-14)
2025-07-07 17:06:33,322 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-14.json
2025-07-07 17:06:33,323 - INFO - 下载内幕交易数据: NVDA (2025-05-14)
2025-07-07 17:06:33,827 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-14.json
2025-07-07 17:06:33,828 - INFO - 下载公司新闻数据: NVDA (2025-05-14)
2025-07-07 17:06:34,331 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-14.json
2025-07-07 17:06:34,331 - INFO - 下载市值数据: NVDA (2025-05-14)
2025-07-07 17:06:34,834 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-14.json
2025-07-07 17:06:34,835 - INFO - 下载财务科目数据: NVDA (2025-05-14)
2025-07-07 17:06:34,868 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:34,868 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:06:34,902 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:35,468 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:35,501 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:35,502 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:35,502 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:35,535 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:39,536 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:39,536 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:06:39,578 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:47,579 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:47,579 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:06:47,617 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:50,902 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:50,902 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:06:50,939 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:53,974 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:53,974 - DEBUG - Starting new HTTPS connection (18): api.financialdatasets.ai:443
2025-07-07 17:06:54,016 - ERROR - 下载财务科目数据失败 MSFT: HTTPSConnectionPool(host='api.financialdatasets.ai', port=443): Max retries exceeded with url: /financials/search/line-items (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
2025-07-07 17:06:54,016 - INFO - 下载财务指标数据: MSFT (2025-03-26)
2025-07-07 17:06:54,519 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-03-26.json
2025-07-07 17:06:54,520 - INFO - 下载内幕交易数据: MSFT (2025-03-26)
2025-07-07 17:06:55,023 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-03-26.json
2025-07-07 17:06:55,024 - INFO - 下载公司新闻数据: MSFT (2025-03-26)
2025-07-07 17:06:55,529 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-03-26.json
2025-07-07 17:06:55,530 - INFO - 下载市值数据: MSFT (2025-03-26)
2025-07-07 17:06:56,032 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-03-26.json
2025-07-07 17:06:56,032 - INFO - 下载财务科目数据: MSFT (2025-03-26)
2025-07-07 17:06:56,664 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:06:56,708 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:06:56,708 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:06:56,708 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:06:57,607 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:06:57,608 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:06:57,609 - INFO - 下载财务指标数据: MSFT (2025-04-02)
2025-07-07 17:06:58,111 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-02.json
2025-07-07 17:06:58,112 - INFO - 下载内幕交易数据: MSFT (2025-04-02)
2025-07-07 17:06:58,613 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-02.json
2025-07-07 17:06:58,613 - INFO - 下载公司新闻数据: MSFT (2025-04-02)
2025-07-07 17:06:59,118 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-02.json
2025-07-07 17:06:59,119 - INFO - 下载市值数据: MSFT (2025-04-02)
2025-07-07 17:06:59,621 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-02.json
2025-07-07 17:06:59,624 - INFO - 下载财务科目数据: MSFT (2025-04-02)
2025-07-07 17:07:00,258 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:00,301 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:00,301 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:00,301 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:01,212 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:01,213 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:01,213 - INFO - 下载财务指标数据: MSFT (2025-04-09)
2025-07-07 17:07:01,714 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-09.json
2025-07-07 17:07:01,715 - INFO - 下载内幕交易数据: MSFT (2025-04-09)
2025-07-07 17:07:02,218 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-09.json
2025-07-07 17:07:02,219 - INFO - 下载公司新闻数据: MSFT (2025-04-09)
2025-07-07 17:07:02,726 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-09.json
2025-07-07 17:07:02,726 - INFO - 下载市值数据: MSFT (2025-04-09)
2025-07-07 17:07:03,229 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-09.json
2025-07-07 17:07:03,229 - INFO - 下载财务科目数据: MSFT (2025-04-09)
2025-07-07 17:07:03,618 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:03,618 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:07:03,655 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:03,862 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:04,736 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:04,737 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:04,739 - INFO - 下载财务指标数据: MSFT (2025-04-16)
2025-07-07 17:07:05,241 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-16.json
2025-07-07 17:07:05,242 - INFO - 下载内幕交易数据: MSFT (2025-04-16)
2025-07-07 17:07:05,744 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-16.json
2025-07-07 17:07:05,744 - INFO - 下载公司新闻数据: MSFT (2025-04-16)
2025-07-07 17:07:06,246 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-16.json
2025-07-07 17:07:06,246 - INFO - 下载市值数据: MSFT (2025-04-16)
2025-07-07 17:07:06,747 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-16.json
2025-07-07 17:07:06,747 - INFO - 下载财务科目数据: MSFT (2025-04-16)
2025-07-07 17:07:07,381 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:07,422 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:07,422 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:07,422 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:07,474 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:11,475 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:11,475 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:11,513 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:19,513 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:19,514 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:07:19,555 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:22,940 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:22,940 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:07:23,985 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:07:24,021 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:24,021 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:24,022 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:07:24,074 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:28,076 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:28,076 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:07:28,118 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:35,556 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:35,557 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:07:35,597 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:35,656 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:35,657 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:07:36,119 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:36,119 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:07:36,699 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:36,700 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:36,700 - INFO - 下载财务指标数据: NVDA (2025-05-21)
2025-07-07 17:07:36,972 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:36,972 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:36,973 - INFO - 下载财务指标数据: AAPL (2025-05-14)
2025-07-07 17:07:37,202 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-21.json
2025-07-07 17:07:37,203 - INFO - 下载内幕交易数据: NVDA (2025-05-21)
2025-07-07 17:07:37,475 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-14.json
2025-07-07 17:07:37,475 - INFO - 下载内幕交易数据: AAPL (2025-05-14)
2025-07-07 17:07:37,706 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-21.json
2025-07-07 17:07:37,708 - INFO - 下载公司新闻数据: NVDA (2025-05-21)
2025-07-07 17:07:37,979 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-14.json
2025-07-07 17:07:37,980 - INFO - 下载公司新闻数据: AAPL (2025-05-14)
2025-07-07 17:07:38,212 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-21.json
2025-07-07 17:07:38,212 - INFO - 下载市值数据: NVDA (2025-05-21)
2025-07-07 17:07:38,482 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-14.json
2025-07-07 17:07:38,484 - INFO - 下载市值数据: AAPL (2025-05-14)
2025-07-07 17:07:38,714 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-21.json
2025-07-07 17:07:38,715 - INFO - 下载财务科目数据: NVDA (2025-05-21)
2025-07-07 17:07:38,986 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-14.json
2025-07-07 17:07:38,986 - INFO - 下载财务科目数据: AAPL (2025-05-14)
2025-07-07 17:07:39,346 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:39,383 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:39,383 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:39,383 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:39,445 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:39,616 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:39,660 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:39,660 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:39,660 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:39,699 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:43,447 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:43,447 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:43,700 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:43,700 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:43,739 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:44,273 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:44,274 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:44,275 - INFO - 下载财务指标数据: NVDA (2025-05-28)
2025-07-07 17:07:44,777 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-05-28.json
2025-07-07 17:07:44,777 - INFO - 下载内幕交易数据: NVDA (2025-05-28)
2025-07-07 17:07:45,281 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-05-28.json
2025-07-07 17:07:45,282 - INFO - 下载公司新闻数据: NVDA (2025-05-28)
2025-07-07 17:07:45,785 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-05-28.json
2025-07-07 17:07:45,785 - INFO - 下载市值数据: NVDA (2025-05-28)
2025-07-07 17:07:46,287 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-05-28.json
2025-07-07 17:07:46,287 - INFO - 下载财务科目数据: NVDA (2025-05-28)
2025-07-07 17:07:46,922 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:47,032 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:47,032 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:47,032 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:48,924 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:48,924 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:48,925 - INFO - 下载财务指标数据: NVDA (2025-06-01)
2025-07-07 17:07:49,428 - DEBUG - 数据已保存到: financial_data_offline\NVDA_financial_metrics\NVDA_financial_metrics_2025-06-01.json
2025-07-07 17:07:49,428 - INFO - 下载内幕交易数据: NVDA (2025-06-01)
2025-07-07 17:07:49,930 - DEBUG - 数据已保存到: financial_data_offline\NVDA_insider_trades\NVDA_insider_trades_2025-06-01.json
2025-07-07 17:07:49,930 - INFO - 下载公司新闻数据: NVDA (2025-06-01)
2025-07-07 17:07:50,432 - DEBUG - 数据已保存到: financial_data_offline\NVDA_company_news\NVDA_company_news_2025-06-01.json
2025-07-07 17:07:50,433 - INFO - 下载市值数据: NVDA (2025-06-01)
2025-07-07 17:07:50,935 - DEBUG - 数据已保存到: financial_data_offline\NVDA_market_cap\NVDA_market_cap_2025-06-01.json
2025-07-07 17:07:50,935 - INFO - 下载财务科目数据: NVDA (2025-06-01)
2025-07-07 17:07:51,566 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:51,724 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:51,724 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:51,724 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:51,740 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:51,740 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:07:51,789 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:52,621 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:52,622 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:52,623 - INFO - 下载财务指标数据: AAPL (2025-05-21)
2025-07-07 17:07:53,125 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-21.json
2025-07-07 17:07:53,126 - INFO - 下载内幕交易数据: AAPL (2025-05-21)
2025-07-07 17:07:53,629 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-21.json
2025-07-07 17:07:53,629 - INFO - 下载公司新闻数据: AAPL (2025-05-21)
2025-07-07 17:07:54,135 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-21.json
2025-07-07 17:07:54,135 - INFO - 下载市值数据: AAPL (2025-05-21)
2025-07-07 17:07:54,636 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-21.json
2025-07-07 17:07:54,637 - INFO - 下载财务科目数据: AAPL (2025-05-21)
2025-07-07 17:07:55,267 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:55,299 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:55,300 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:55,300 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:55,791 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:55,792 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:07:55,837 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:56,172 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:07:56,173 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:07:56,174 - INFO - 下载财务指标数据: AAPL (2025-05-28)
2025-07-07 17:07:56,678 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-05-28.json
2025-07-07 17:07:56,679 - INFO - 下载内幕交易数据: AAPL (2025-05-28)
2025-07-07 17:07:57,182 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-05-28.json
2025-07-07 17:07:57,184 - INFO - 下载公司新闻数据: AAPL (2025-05-28)
2025-07-07 17:07:57,686 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-05-28.json
2025-07-07 17:07:57,687 - INFO - 下载市值数据: AAPL (2025-05-28)
2025-07-07 17:07:58,189 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-05-28.json
2025-07-07 17:07:58,190 - INFO - 下载财务科目数据: AAPL (2025-05-28)
2025-07-07 17:07:58,820 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:07:58,877 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:07:58,878 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:07:58,878 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:07:58,919 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:02,920 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:02,920 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:08:02,961 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:03,838 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:03,839 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:08:03,909 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:07,598 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:07,598 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:08:08,636 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:08:08,672 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:08,674 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:08,674 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:08:08,713 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:10,961 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:10,963 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:08:11,002 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:12,713 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:12,714 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:08:12,758 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:19,910 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:19,910 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:08:19,948 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:20,759 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:20,760 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:08:20,794 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:27,003 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:27,003 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:08:27,047 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:36,796 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:36,797 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:08:36,842 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:51,949 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:51,949 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:08:52,987 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:08:53,026 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:53,026 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:53,027 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:08:53,062 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:57,064 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:57,064 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:08:57,115 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:08:59,048 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:08:59,048 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:09:00,103 - DEBUG - Starting new HTTPS connection (7): api.financialdatasets.ai:443
2025-07-07 17:09:00,147 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:00,147 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:00,147 - DEBUG - Starting new HTTPS connection (8): api.financialdatasets.ai:443
2025-07-07 17:09:00,186 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:04,186 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:04,187 - DEBUG - Starting new HTTPS connection (9): api.financialdatasets.ai:443
2025-07-07 17:09:04,219 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:05,116 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:05,117 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:09:05,169 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:08,843 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:08,843 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:09:10,885 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:09:11,732 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:11,733 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:11,733 - INFO - 下载财务指标数据: MSFT (2025-04-23)
2025-07-07 17:09:12,220 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:12,221 - DEBUG - Starting new HTTPS connection (10): api.financialdatasets.ai:443
2025-07-07 17:09:12,234 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-23.json
2025-07-07 17:09:12,235 - INFO - 下载内幕交易数据: MSFT (2025-04-23)
2025-07-07 17:09:12,262 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:12,737 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-23.json
2025-07-07 17:09:12,737 - INFO - 下载公司新闻数据: MSFT (2025-04-23)
2025-07-07 17:09:13,242 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-23.json
2025-07-07 17:09:13,242 - INFO - 下载市值数据: MSFT (2025-04-23)
2025-07-07 17:09:13,746 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-23.json
2025-07-07 17:09:13,747 - INFO - 下载财务科目数据: MSFT (2025-04-23)
2025-07-07 17:09:14,377 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:14,418 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:14,418 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:14,418 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:14,463 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:18,464 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:18,464 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:19,304 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:19,305 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:19,305 - INFO - 下载财务指标数据: MSFT (2025-04-30)
2025-07-07 17:09:19,808 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-04-30.json
2025-07-07 17:09:19,809 - INFO - 下载内幕交易数据: MSFT (2025-04-30)
2025-07-07 17:09:20,312 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-04-30.json
2025-07-07 17:09:20,312 - INFO - 下载公司新闻数据: MSFT (2025-04-30)
2025-07-07 17:09:20,818 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-04-30.json
2025-07-07 17:09:20,818 - INFO - 下载市值数据: MSFT (2025-04-30)
2025-07-07 17:09:21,171 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:21,171 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:09:21,319 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-04-30.json
2025-07-07 17:09:21,319 - INFO - 下载财务科目数据: MSFT (2025-04-30)
2025-07-07 17:09:21,956 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:22,000 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:22,000 - ERROR - 下载财务科目数据失败 NVDA: Error fetching data: NVDA - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:22,000 - INFO - 完成下载: NVDA
2025-07-07 17:09:22,784 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:22,785 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:22,787 - INFO - 下载财务指标数据: MSFT (2025-05-07)
2025-07-07 17:09:23,291 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-07.json
2025-07-07 17:09:23,292 - INFO - 下载内幕交易数据: MSFT (2025-05-07)
2025-07-07 17:09:23,794 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-07.json
2025-07-07 17:09:23,794 - INFO - 下载公司新闻数据: MSFT (2025-05-07)
2025-07-07 17:09:24,300 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-07.json
2025-07-07 17:09:24,301 - INFO - 下载市值数据: MSFT (2025-05-07)
2025-07-07 17:09:24,802 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-07.json
2025-07-07 17:09:24,802 - INFO - 下载财务科目数据: MSFT (2025-05-07)
2025-07-07 17:09:25,436 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:25,475 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:25,476 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:25,476 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:25,514 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:28,263 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:28,264 - DEBUG - Starting new HTTPS connection (11): api.financialdatasets.ai:443
2025-07-07 17:09:28,304 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:29,514 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:29,515 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:30,363 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:30,363 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:30,364 - INFO - 下载财务指标数据: MSFT (2025-05-14)
2025-07-07 17:09:30,866 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-14.json
2025-07-07 17:09:30,867 - INFO - 下载内幕交易数据: MSFT (2025-05-14)
2025-07-07 17:09:31,370 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-14.json
2025-07-07 17:09:31,372 - INFO - 下载公司新闻数据: MSFT (2025-05-14)
2025-07-07 17:09:31,874 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-14.json
2025-07-07 17:09:31,874 - INFO - 下载市值数据: MSFT (2025-05-14)
2025-07-07 17:09:32,376 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-14.json
2025-07-07 17:09:32,377 - INFO - 下载财务科目数据: MSFT (2025-05-14)
2025-07-07 17:09:33,004 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:33,043 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:33,044 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:33,044 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:33,079 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:37,080 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:37,081 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:37,936 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:37,936 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:37,937 - INFO - 下载财务指标数据: MSFT (2025-05-21)
2025-07-07 17:09:38,441 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-21.json
2025-07-07 17:09:38,441 - INFO - 下载内幕交易数据: MSFT (2025-05-21)
2025-07-07 17:09:38,946 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-21.json
2025-07-07 17:09:38,947 - INFO - 下载公司新闻数据: MSFT (2025-05-21)
2025-07-07 17:09:39,450 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-21.json
2025-07-07 17:09:39,452 - INFO - 下载市值数据: MSFT (2025-05-21)
2025-07-07 17:09:39,953 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-21.json
2025-07-07 17:09:39,954 - INFO - 下载财务科目数据: MSFT (2025-05-21)
2025-07-07 17:09:40,582 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:40,625 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:40,625 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:40,626 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:41,500 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:09:41,500 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:09:41,501 - INFO - 下载财务指标数据: MSFT (2025-05-28)
2025-07-07 17:09:42,003 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-05-28.json
2025-07-07 17:09:42,004 - INFO - 下载内幕交易数据: MSFT (2025-05-28)
2025-07-07 17:09:42,508 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-05-28.json
2025-07-07 17:09:42,509 - INFO - 下载公司新闻数据: MSFT (2025-05-28)
2025-07-07 17:09:43,014 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-05-28.json
2025-07-07 17:09:43,015 - INFO - 下载市值数据: MSFT (2025-05-28)
2025-07-07 17:09:43,516 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-05-28.json
2025-07-07 17:09:43,516 - INFO - 下载财务科目数据: MSFT (2025-05-28)
2025-07-07 17:09:44,152 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:09:44,187 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:44,188 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:44,188 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:09:44,221 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:48,222 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:48,222 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:09:48,270 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:09:56,271 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:09:56,271 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:09:56,311 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:00,305 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:00,306 - DEBUG - Starting new HTTPS connection (12): api.financialdatasets.ai:443
2025-07-07 17:10:02,345 - DEBUG - Starting new HTTPS connection (13): api.financialdatasets.ai:443
2025-07-07 17:10:02,380 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:02,381 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:02,381 - DEBUG - Starting new HTTPS connection (14): api.financialdatasets.ai:443
2025-07-07 17:10:02,415 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:06,416 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:06,416 - DEBUG - Starting new HTTPS connection (15): api.financialdatasets.ai:443
2025-07-07 17:10:06,456 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:12,312 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:12,312 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:10:13,160 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:13,160 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:13,160 - INFO - 下载财务指标数据: MSFT (2025-06-01)
2025-07-07 17:10:13,663 - DEBUG - 数据已保存到: financial_data_offline\MSFT_financial_metrics\MSFT_financial_metrics_2025-06-01.json
2025-07-07 17:10:13,664 - INFO - 下载内幕交易数据: MSFT (2025-06-01)
2025-07-07 17:10:14,167 - DEBUG - 数据已保存到: financial_data_offline\MSFT_insider_trades\MSFT_insider_trades_2025-06-01.json
2025-07-07 17:10:14,168 - INFO - 下载公司新闻数据: MSFT (2025-06-01)
2025-07-07 17:10:14,458 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:14,459 - DEBUG - Starting new HTTPS connection (16): api.financialdatasets.ai:443
2025-07-07 17:10:14,674 - DEBUG - 数据已保存到: financial_data_offline\MSFT_company_news\MSFT_company_news_2025-06-01.json
2025-07-07 17:10:14,674 - INFO - 下载市值数据: MSFT (2025-06-01)
2025-07-07 17:10:15,175 - DEBUG - 数据已保存到: financial_data_offline\MSFT_market_cap\MSFT_market_cap_2025-06-01.json
2025-07-07 17:10:15,176 - INFO - 下载财务科目数据: MSFT (2025-06-01)
2025-07-07 17:10:15,404 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:15,405 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:15,407 - INFO - 下载财务指标数据: AAPL (2025-06-01)
2025-07-07 17:10:15,811 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:10:15,855 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:15,856 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:15,857 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:10:15,899 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:15,910 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-06-01.json
2025-07-07 17:10:15,911 - INFO - 下载内幕交易数据: AAPL (2025-06-01)
2025-07-07 17:10:16,413 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-06-01.json
2025-07-07 17:10:16,414 - INFO - 下载公司新闻数据: AAPL (2025-06-01)
2025-07-07 17:10:16,919 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-06-01.json
2025-07-07 17:10:16,920 - INFO - 下载市值数据: AAPL (2025-06-01)
2025-07-07 17:10:17,421 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-06-01.json
2025-07-07 17:10:17,422 - INFO - 下载财务科目数据: AAPL (2025-06-01)
2025-07-07 17:10:18,055 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:10:18,089 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:10:18,090 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:18,090 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:10:19,002 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:19,002 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:19,002 - INFO - 完成下载: AAPL
2025-07-07 17:10:19,900 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:10:19,900 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:10:21,896 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 400 104
2025-07-07 17:10:21,897 - ERROR - 下载财务科目数据失败 MSFT: Error fetching data: MSFT - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 17:10:21,897 - INFO - 完成下载: MSFT
2025-07-07 17:10:21,902 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_171021.txt
2025-07-07 17:24:49,633 - INFO - 开始下载数据: 1 个股票, 1 种数据类型
2025-07-07 17:24:49,634 - INFO - 时间范围: 2025-01-01 到 2025-01-07
2025-07-07 17:24:49,634 - INFO - 数据类型: line_items
2025-07-07 17:24:49,634 - INFO - AAPL line_items: 生成 2 个采样日期
2025-07-07 17:24:49,635 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 17:24:50,274 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:24:51,180 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:24:51,183 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-01.json
2025-07-07 17:24:51,184 - INFO - 下载财务科目数据: AAPL (2025-01-07)
2025-07-07 17:24:51,816 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:24:51,909 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:24:51,910 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:24:51,910 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:24:52,783 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:24:52,784 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-07.json
2025-07-07 17:24:52,785 - INFO - 完成下载: AAPL
2025-07-07 17:24:52,786 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_172452.txt
2025-07-07 17:25:12,454 - INFO - 开始下载数据: 1 个股票, 6 种数据类型
2025-07-07 17:25:12,454 - INFO - 时间范围: 2025-01-01 到 2025-01-10
2025-07-07 17:25:12,454 - INFO - 数据类型: prices, market_cap, company_news, financial_metrics, line_items, insider_trades
2025-07-07 17:25:12,456 - INFO - 下载股价数据: AAPL (2025-01-01 to 2025-01-10)
2025-07-07 17:25:13,090 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:25:13,174 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:13,175 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10
2025-07-07 17:25:13,176 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:25:13,258 - DEBUG - Incremented Retry for (url='/prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:17,259 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10
2025-07-07 17:25:17,259 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:25:19,545 - DEBUG - https://api.financialdatasets.ai:443 "GET /prices/?ticker=AAPL&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-01-10 HTTP/1.1" 200 None
2025-07-07 17:25:19,552 - DEBUG - 数据已保存到: financial_data_offline\AAPL_prices\AAPL_prices_2025-01-01_to_2025-01-10.json
2025-07-07 17:25:19,552 - INFO - AAPL financial_metrics: 生成 2 个采样日期
2025-07-07 17:25:19,552 - INFO - 财务指标数据已存在: AAPL (2025-01-01)
2025-07-07 17:25:19,553 - INFO - 下载财务指标数据: AAPL (2025-01-10)
2025-07-07 17:25:20,186 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:25:20,653 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:20,653 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:20,653 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:25:20,736 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=3, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:24,737 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:24,738 - DEBUG - Starting new HTTPS connection (3): api.financialdatasets.ai:443
2025-07-07 17:25:25,098 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=2, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:33,099 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:33,099 - DEBUG - Starting new HTTPS connection (4): api.financialdatasets.ai:443
2025-07-07 17:25:33,381 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=1, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:25:49,382 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:25:49,382 - DEBUG - Starting new HTTPS connection (5): api.financialdatasets.ai:443
2025-07-07 17:25:51,158 - DEBUG - Incremented Retry for (url='/financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm'): Retry(total=0, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:26:23,159 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm
2025-07-07 17:26:23,160 - DEBUG - Starting new HTTPS connection (6): api.financialdatasets.ai:443
2025-07-07 17:26:24,772 - DEBUG - https://api.financialdatasets.ai:443 "GET /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-10&limit=10&period=ttm HTTP/1.1" 200 None
2025-07-07 17:26:25,052 - DEBUG - 数据已保存到: financial_data_offline\AAPL_financial_metrics\AAPL_financial_metrics_2025-01-10.json
2025-07-07 17:26:25,053 - INFO - AAPL insider_trades: 生成 3 个采样日期
2025-07-07 17:26:25,053 - INFO - 内幕交易数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:25,054 - INFO - 内幕交易数据已存在: AAPL (2025-01-08)
2025-07-07 17:26:25,054 - INFO - 下载内幕交易数据: AAPL (2025-01-10)
2025-07-07 17:26:25,687 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:26:27,486 - DEBUG - https://api.financialdatasets.ai:443 "GET /insider-trades/?ticker=AAPL&filing_date_lte=2025-01-10&limit=50 HTTP/1.1" 200 None
2025-07-07 17:26:27,922 - DEBUG - 数据已保存到: financial_data_offline\AAPL_insider_trades\AAPL_insider_trades_2025-01-10.json
2025-07-07 17:26:27,923 - INFO - AAPL company_news: 生成 8 个采样日期
2025-07-07 17:26:27,924 - INFO - 公司新闻数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:27,924 - INFO - 下载公司新闻数据: AAPL (2025-01-02)
2025-07-07 17:26:28,567 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:26:28,644 - DEBUG - Incremented Retry for (url='/news/?ticker=AAPL&end_date=2025-01-02&limit=100'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:26:28,644 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=AAPL&end_date=2025-01-02&limit=100
2025-07-07 17:26:28,644 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:26:29,757 - DEBUG - https://api.financialdatasets.ai:443 "GET /news/?ticker=AAPL&end_date=2025-01-02&limit=100 HTTP/1.1" 200 None
2025-07-07 17:26:30,204 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-02.json
2025-07-07 17:26:30,205 - INFO - 下载公司新闻数据: AAPL (2025-01-03)
2025-07-07 17:26:30,710 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-03.json
2025-07-07 17:26:30,711 - INFO - 下载公司新闻数据: AAPL (2025-01-06)
2025-07-07 17:26:31,216 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-06.json
2025-07-07 17:26:31,217 - INFO - 下载公司新闻数据: AAPL (2025-01-07)
2025-07-07 17:26:31,720 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-07.json
2025-07-07 17:26:31,721 - INFO - 公司新闻数据已存在: AAPL (2025-01-08)
2025-07-07 17:26:31,722 - INFO - 下载公司新闻数据: AAPL (2025-01-09)
2025-07-07 17:26:32,226 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-09.json
2025-07-07 17:26:32,227 - INFO - 下载公司新闻数据: AAPL (2025-01-10)
2025-07-07 17:26:32,732 - DEBUG - 数据已保存到: financial_data_offline\AAPL_company_news\AAPL_company_news_2025-01-10.json
2025-07-07 17:26:32,732 - INFO - AAPL market_cap: 生成 8 个采样日期
2025-07-07 17:26:32,733 - INFO - 市值数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:32,733 - INFO - 下载市值数据: AAPL (2025-01-02)
2025-07-07 17:26:33,236 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-02.json
2025-07-07 17:26:33,236 - INFO - 下载市值数据: AAPL (2025-01-03)
2025-07-07 17:26:33,737 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-03.json
2025-07-07 17:26:33,738 - INFO - 下载市值数据: AAPL (2025-01-06)
2025-07-07 17:26:34,240 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-06.json
2025-07-07 17:26:34,241 - INFO - 下载市值数据: AAPL (2025-01-07)
2025-07-07 17:26:34,743 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-07.json
2025-07-07 17:26:34,743 - INFO - 市值数据已存在: AAPL (2025-01-08)
2025-07-07 17:26:34,743 - INFO - 下载市值数据: AAPL (2025-01-09)
2025-07-07 17:26:35,244 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-09.json
2025-07-07 17:26:35,245 - INFO - 下载市值数据: AAPL (2025-01-10)
2025-07-07 17:26:35,746 - DEBUG - 数据已保存到: financial_data_offline\AAPL_market_cap\AAPL_market_cap_2025-01-10.json
2025-07-07 17:26:35,747 - INFO - AAPL line_items: 生成 2 个采样日期
2025-07-07 17:26:35,747 - INFO - 财务科目数据已存在: AAPL (2025-01-01)
2025-07-07 17:26:35,747 - INFO - 下载财务科目数据: AAPL (2025-01-10)
2025-07-07 17:26:36,382 - DEBUG - Starting new HTTPS connection (1): api.financialdatasets.ai:443
2025-07-07 17:26:36,454 - DEBUG - Incremented Retry for (url='/financials/search/line-items'): Retry(total=4, connect=None, read=None, redirect=None, status=None)
2025-07-07 17:26:36,455 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 17:26:36,455 - DEBUG - Starting new HTTPS connection (2): api.financialdatasets.ai:443
2025-07-07 17:26:37,431 - DEBUG - https://api.financialdatasets.ai:443 "POST /financials/search/line-items HTTP/1.1" 200 None
2025-07-07 17:26:37,432 - DEBUG - 数据已保存到: financial_data_offline\AAPL_line_items\AAPL_line_items_2025-01-10.json
2025-07-07 17:26:37,432 - INFO - 完成下载: AAPL
2025-07-07 17:26:37,434 - INFO - 报告已保存到: financial_data_offline\download_report_20250707_172637.txt
