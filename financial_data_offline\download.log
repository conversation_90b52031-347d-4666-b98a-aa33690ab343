2025-07-07 16:55:40,804 - INFO - 开始下载数据: 3 个股票, 6 种数据类型
2025-07-07 16:55:40,806 - INFO - 时间范围: 2025-01-01 到 2025-06-01
2025-07-07 16:55:40,806 - INFO - 数据类型: prices, financial_metrics, insider_trades, company_news, market_cap, line_items
2025-07-07 16:55:40,807 - INFO - 下载股价数据: AAPL (2025-01-01 to 2025-06-01)
2025-07-07 16:55:40,807 - INFO - 下载股价数据: MSFT (2025-01-01 to 2025-06-01)
2025-07-07 16:55:40,808 - INFO - 下载股价数据: NVDA (2025-01-01 to 2025-06-01)
2025-07-07 16:55:41,600 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:42,878 - INFO - 下载财务指标数据: AAPL (2025-01-01)
2025-07-07 16:55:42,880 - INFO - 下载财务指标数据: MSFT (2025-01-01)
2025-07-07 16:55:43,615 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:43,689 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:45,640 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:47,688 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:47,801 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:53,709 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:55:55,786 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:55:56,042 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:09,761 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:56:11,892 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:12,276 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:41,842 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01
2025-07-07 16:56:41,985 - ERROR - 下载股价数据失败 NVDA: HTTPSConnectionPool(host='api.financialdatasets.ai', port=443): Max retries exceeded with url: /prices/?ticker=NVDA&interval=day&interval_multiplier=1&start_date=2025-01-01&end_date=2025-06-01 (Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')))
2025-07-07 16:56:41,988 - INFO - 下载财务指标数据: NVDA (2025-01-01)
2025-07-07 16:56:43,829 - INFO - 下载内幕交易数据: NVDA (2025-01-01)
2025-07-07 16:56:44,555 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=NVDA&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:44,580 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=AAPL&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:44,872 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:45,719 - INFO - 下载内幕交易数据: AAPL (2025-01-01)
2025-07-07 16:56:46,028 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:46,389 - INFO - 下载公司新闻数据: NVDA (2025-01-01)
2025-07-07 16:56:47,113 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:56:47,540 - INFO - 下载公司新闻数据: AAPL (2025-01-01)
2025-07-07 16:56:49,630 - INFO - 下载市值数据: AAPL (2025-01-01)
2025-07-07 16:56:50,129 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financial-metrics/?ticker=MSFT&report_period_lte=2025-01-01&limit=10&period=ttm
2025-07-07 16:56:50,134 - INFO - 下载财务科目数据: AAPL (2025-01-01)
2025-07-07 16:56:51,248 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
2025-07-07 16:56:51,296 - INFO - 下载内幕交易数据: MSFT (2025-01-01)
2025-07-07 16:56:51,672 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:56:51,672 - INFO - 下载财务指标数据: AAPL (2025-01-08)
2025-07-07 16:56:51,978 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:52,176 - INFO - 下载内幕交易数据: AAPL (2025-01-08)
2025-07-07 16:56:52,679 - INFO - 下载公司新闻数据: AAPL (2025-01-08)
2025-07-07 16:56:53,182 - INFO - 下载市值数据: AAPL (2025-01-08)
2025-07-07 16:56:53,684 - INFO - 下载财务科目数据: AAPL (2025-01-08)
2025-07-07 16:56:55,270 - ERROR - 下载财务科目数据失败 AAPL: Error fetching data: AAPL - 400 - {"error": "Invalid line items: operating_cash_flow, total_equity, long_term_debt, capital_expenditures"}
2025-07-07 16:56:55,271 - INFO - 下载财务指标数据: AAPL (2025-01-15)
2025-07-07 16:56:55,774 - INFO - 下载内幕交易数据: AAPL (2025-01-15)
2025-07-07 16:56:56,029 - WARNING - Retrying (Retry(total=3, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /insider-trades/?ticker=MSFT&filing_date_lte=2025-01-01&limit=50
2025-07-07 16:56:56,279 - INFO - 下载公司新闻数据: AAPL (2025-01-15)
2025-07-07 16:56:56,782 - INFO - 下载市值数据: AAPL (2025-01-15)
2025-07-07 16:56:57,284 - INFO - 下载财务科目数据: AAPL (2025-01-15)
2025-07-07 16:56:57,931 - INFO - 下载公司新闻数据: MSFT (2025-01-01)
2025-07-07 16:56:57,939 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /financials/search/line-items
2025-07-07 16:56:58,665 - WARNING - Retrying (Retry(total=4, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=MSFT&end_date=2025-01-01&limit=100
2025-07-07 16:56:59,542 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1000)')': /news/?ticker=NVDA&end_date=2025-01-01&limit=100
