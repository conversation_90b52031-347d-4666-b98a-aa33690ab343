#!/usr/bin/env python3
"""
清理财务数据离线存储目录中的所有JSON文件
保留目录结构，但删除所有数据文件
"""

import os
import glob
import logging

def cleanup_financial_data():
    """清理financial_data_offline目录中的所有JSON文件"""
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    # 定义基础目录
    base_dir = "financial_data_offline"
    
    if not os.path.exists(base_dir):
        logger.warning(f"目录 {base_dir} 不存在")
        return
    
    # 查找所有JSON文件
    json_pattern = os.path.join(base_dir, "**", "*.json")
    json_files = glob.glob(json_pattern, recursive=True)
    
    logger.info(f"找到 {len(json_files)} 个JSON文件需要删除")
    
    deleted_count = 0
    error_count = 0
    
    # 删除所有JSON文件
    for json_file in json_files:
        try:
            os.remove(json_file)
            deleted_count += 1
            if deleted_count % 50 == 0:  # 每删除50个文件报告一次进度
                logger.info(f"已删除 {deleted_count} 个文件...")
        except Exception as e:
            logger.error(f"删除文件 {json_file} 失败: {e}")
            error_count += 1
    
    logger.info(f"清理完成:")
    logger.info(f"  - 成功删除: {deleted_count} 个文件")
    logger.info(f"  - 删除失败: {error_count} 个文件")
    
    # 显示剩余的目录结构
    logger.info(f"\n保留的目录结构:")
    for root, dirs, files in os.walk(base_dir):
        level = root.replace(base_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        logger.info(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            if not file.endswith('.json'):
                logger.info(f"{subindent}{file}")

if __name__ == "__main__":
    cleanup_financial_data()
