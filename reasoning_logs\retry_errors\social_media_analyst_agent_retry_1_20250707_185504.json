{"error_type": "ValueError", "error_message": "Failed to create Pydantic model from parsed JSON: 1 validation error for SocialMediaAnalysisSignal\nsocial_influence_analysis\n  Field required [type=missing, input_value={'signal': 'neutral', 'co... on public perception.'}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.11/v/missing", "model_name": "glm-4-flash", "model_provider": "QingYun", "agent_name": "social_media_analyst_agent", "attempt_number": 1, "max_retries": 3, "timestamp": "2025-07-07T18:55:04.567438"}