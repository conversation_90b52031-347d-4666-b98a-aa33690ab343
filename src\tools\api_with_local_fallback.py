#!/usr/bin/env python3
"""
增强版API工具，支持本地财务数据回退
当启用本地数据模式时，优先使用本地数据，API作为回退
"""

import logging
from typing import Any, Optional, List
from datetime import datetime

# 导入原始API函数
from src.tools.api import (
    get_prices as _api_get_prices,
    get_financial_metrics as _api_get_financial_metrics,
    search_line_items as _api_search_line_items,
    get_insider_trades as _api_get_insider_trades,
    get_company_news as _api_get_company_news,
    get_market_cap as _api_get_market_cap,
    get_current_price as _api_get_current_price,
    get_dynamic_market_context as _api_get_dynamic_market_context,
    prices_to_df as _api_prices_to_df,
    get_price_data as _api_get_price_data,
    get_formatted_multi_source_news as _api_get_formatted_multi_source_news,
)

# 导入本地数据读取器
from src.tools.local_financial_data_reader import (
    load_local_prices,
    load_local_financial_metrics,
    load_local_insider_trades,
    load_local_company_news,
    load_local_market_cap,
    load_local_line_items
)

# 导入配置
from src.config.financial_data_config import (
    should_use_local_financial_data,
    get_local_financial_data_directory,
    check_local_financial_data_availability
)

logger = logging.getLogger(__name__)


def get_prices(ticker: str, start_date: str, end_date: str, agent_name: Optional[str] = None) -> Any:
    """
    获取股价数据，支持本地数据回退
    
    Args:
        ticker: 股票代码
        start_date: 开始日期
        end_date: 结束日期
        agent_name: 代理名称
    
    Returns:
        股价数据
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载股价数据: {ticker} ({start_date} to {end_date})")
        
        try:
            local_data = load_local_prices(
                ticker=ticker,
                start_date=start_date,
                end_date=end_date,
                base_dir=get_local_financial_data_directory()
            )
            
            if local_data is not None:
                logger.info(f"✓ 成功从本地加载股价数据: {ticker}")
                return local_data
            else:
                logger.warning(f"本地股价数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"加载本地股价数据失败: {ticker}, 错误: {e}, 回退到API")
    
    # 回退到API调用
    logger.info(f"使用API获取股价数据: {ticker}")
    return _api_get_prices(ticker, start_date, end_date, agent_name)


def get_financial_metrics(ticker: str, end_date: str, period: str = "ttm",
                         limit: int = 10, agent_name: Optional[str] = None) -> Any:
    """
    获取财务指标数据，支持本地数据回退
    
    Args:
        ticker: 股票代码
        end_date: 结束日期
        period: 时期
        limit: 限制数量
        agent_name: 代理名称
    
    Returns:
        财务指标数据
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载财务指标数据: {ticker} ({end_date})")
        
        try:
            local_data = load_local_financial_metrics(
                ticker=ticker,
                end_date=end_date,
                base_dir=get_local_financial_data_directory()
            )
            
            if local_data is not None:
                logger.info(f"✓ 成功从本地加载财务指标数据: {ticker}")
                return local_data
            else:
                logger.warning(f"本地财务指标数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"加载本地财务指标数据失败: {ticker}, 错误: {e}, 回退到API")
    
    # 回退到API调用
    logger.info(f"使用API获取财务指标数据: {ticker}")
    return _api_get_financial_metrics(ticker, end_date, period, limit, agent_name)


def search_line_items(ticker: str, line_items: List[str], end_date: str,
                     period: str = "ttm", agent_name: Optional[str] = None) -> Any:
    """
    搜索财务科目数据，支持本地数据回退
    
    Args:
        ticker: 股票代码
        line_items: 财务科目列表
        end_date: 结束日期
        period: 时期
        agent_name: 代理名称
    
    Returns:
        财务科目数据
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载财务科目数据: {ticker} ({end_date})")
        
        try:
            local_data = load_local_line_items(
                ticker=ticker,
                end_date=end_date,
                base_dir=get_local_financial_data_directory()
            )
            
            if local_data is not None:
                logger.info(f"✓ 成功从本地加载财务科目数据: {ticker}")
                return local_data
            else:
                logger.warning(f"本地财务科目数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"加载本地财务科目数据失败: {ticker}, 错误: {e}, 回退到API")
    
    # 回退到API调用
    logger.info(f"使用API获取财务科目数据: {ticker}")
    return _api_search_line_items(ticker, line_items, end_date, period, limit=10, agent_name=agent_name)


def get_insider_trades(ticker: str, end_date: str, start_date: Optional[str] = None, limit: int = 20, agent_name: Optional[str] = None) -> Any:
    """
    获取内幕交易数据，支持本地数据回退

    Args:
        ticker: 股票代码
        end_date: 结束日期
        limit: 限制数量
        agent_name: 代理名称

    Returns:
        内幕交易数据
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载内幕交易数据: {ticker} ({end_date})")

        try:
            local_data = load_local_insider_trades(
                ticker=ticker,
                end_date=end_date,
                base_dir=get_local_financial_data_directory()
            )

            if local_data is not None:
                logger.info(f"✓ 成功从本地加载内幕交易数据: {ticker}")
                return local_data
            else:
                logger.warning(f"本地内幕交易数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"加载本地内幕交易数据失败: {ticker}, 错误: {e}, 回退到API")

    # 回退到API调用
    logger.info(f"使用API获取内幕交易数据: {ticker}")
    return _api_get_insider_trades(ticker, end_date, start_date, limit, agent_name)


def get_company_news(ticker: str, end_date: str, start_date: Optional[str] = None, limit: int = 100, agent_name: Optional[str] = None) -> Any:
    """
    获取公司新闻数据，支持本地数据回退
    
    Args:
        ticker: 股票代码
        end_date: 结束日期
        limit: 限制数量
        agent_name: 代理名称
    
    Returns:
        公司新闻数据
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载公司新闻数据: {ticker} ({end_date})")
        
        try:
            local_data = load_local_company_news(
                ticker=ticker,
                end_date=end_date,
                base_dir=get_local_financial_data_directory()
            )
            
            if local_data is not None:
                logger.info(f"✓ 成功从本地加载公司新闻数据: {ticker}")
                return local_data
            else:
                logger.warning(f"本地公司新闻数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"加载本地公司新闻数据失败: {ticker}, 错误: {e}, 回退到API")
    
    # 回退到API调用
    logger.info(f"使用API获取公司新闻数据: {ticker}")
    return _api_get_company_news(ticker, end_date, start_date, limit, agent_name)


def get_market_cap(ticker: str, end_date: str) -> Any:
    """
    获取市值数据，支持本地数据回退
    
    Args:
        ticker: 股票代码
        end_date: 结束日期
    
    Returns:
        市值数据
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载市值数据: {ticker} ({end_date})")
        
        try:
            local_data = load_local_market_cap(
                ticker=ticker,
                date=end_date,
                base_dir=get_local_financial_data_directory()
            )
            
            if local_data is not None:
                logger.info(f"✓ 成功从本地加载市值数据: {ticker}")
                return local_data
            else:
                logger.warning(f"本地市值数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"加载本地市值数据失败: {ticker}, 错误: {e}, 回退到API")
    
    # 回退到API调用
    logger.info(f"使用API获取市值数据: {ticker}")
    return _api_get_market_cap(ticker, end_date)





def check_data_source_status(ticker: str) -> dict:
    """
    检查数据源状态
    
    Args:
        ticker: 股票代码
    
    Returns:
        数据源状态信息
    """
    status = {
        "use_local_data": should_use_local_financial_data(),
        "local_data_directory": get_local_financial_data_directory(),
        "local_data_availability": {}
    }
    
    if should_use_local_financial_data():
        data_types = ["prices", "financial_metrics", "insider_trades", 
                     "company_news", "market_cap", "line_items"]
        
        for data_type in data_types:
            status["local_data_availability"][data_type] = check_local_financial_data_availability(ticker, data_type)
    
    return status


def print_data_source_info(ticker: str):
    """
    打印数据源信息
    
    Args:
        ticker: 股票代码
    """
    status = check_data_source_status(ticker)
    
    print(f"\n数据源信息 - {ticker}")
    print("=" * 40)
    print(f"使用本地数据: {'是' if status['use_local_data'] else '否'}")
    
    if status['use_local_data']:
        print(f"本地数据目录: {status['local_data_directory']}")
        print("\n本地数据可用性:")
        for data_type, available in status['local_data_availability'].items():
            status_icon = "✓" if available else "✗"
            print(f"  {status_icon} {data_type}")
    else:
        print("数据源: FINANCIAL_DATASETS_API")
    
    print("=" * 40)


# 添加其他必要的API函数包装器

def get_current_price(ticker: str, end_date: str) -> Any:
    """
    获取当前价格，支持本地数据回退

    Args:
        ticker: 股票代码
        end_date: 结束日期

    Returns:
        当前价格
    """
    # 检查是否使用本地数据
    if should_use_local_financial_data():
        logger.info(f"尝试从本地加载价格数据获取当前价格: {ticker} ({end_date})")

        try:
            # 尝试从本地价格数据中获取当前价格
            local_data = load_local_prices(
                ticker=ticker,
                start_date=end_date,
                end_date=end_date,
                base_dir=get_local_financial_data_directory()
            )

            if local_data and len(local_data) > 0:
                # 从价格数据中提取收盘价
                current_price = local_data[-1].close if hasattr(local_data[-1], 'close') else None
                if current_price:
                    logger.info(f"✓ 成功从本地数据获取当前价格: {ticker} = {current_price}")
                    return current_price
                else:
                    logger.warning(f"本地价格数据格式不正确: {ticker}, 回退到API")
            else:
                logger.warning(f"本地价格数据不可用: {ticker}, 回退到API")
        except Exception as e:
            logger.error(f"从本地数据获取当前价格失败: {ticker}, 错误: {e}, 回退到API")

    # 回退到API调用
    logger.info(f"使用API获取当前价格: {ticker}")
    return _api_get_current_price(ticker, end_date)


def get_dynamic_market_context(ticker: str, end_date: str, agent_name: Optional[str] = None) -> Any:
    """
    获取动态市场上下文，直接使用API（不需要本地缓存）

    Args:
        ticker: 股票代码
        end_date: 结束日期
        agent_name: 代理名称

    Returns:
        动态市场上下文
    """
    logger.info(f"获取动态市场上下文: {ticker}")
    return _api_get_dynamic_market_context(ticker, end_date, agent_name)


def prices_to_df(prices: Any) -> Any:
    """
    将价格数据转换为DataFrame，直接使用原始函数

    Args:
        prices: 价格数据

    Returns:
        DataFrame格式的价格数据
    """
    return _api_prices_to_df(prices)


def get_price_data(ticker: str, start_date: str, end_date: str) -> Any:
    """
    获取价格数据并转换为DataFrame，支持本地数据回退

    Args:
        ticker: 股票代码
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        DataFrame格式的价格数据
    """
    # 使用本地数据优先的get_prices函数
    prices = get_prices(ticker, start_date, end_date)
    return prices_to_df(prices)


def get_formatted_multi_source_news(ticker: str, limit: int = 10, sources: Optional[List[str]] = None,
                                   date: Optional[str] = None, agent_name: Optional[str] = None) -> Any:
    """
    获取多源新闻数据，直接使用API（新闻数据通常需要实时获取）

    Args:
        ticker: 股票代码
        limit: 限制数量
        sources: 新闻源列表
        date: 日期
        agent_name: 代理名称

    Returns:
        格式化的多源新闻数据
    """
    logger.info(f"获取多源新闻数据: {ticker}")
    return _api_get_formatted_multi_source_news(ticker, limit, sources, date, agent_name)
