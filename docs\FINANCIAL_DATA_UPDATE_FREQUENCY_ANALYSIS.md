# 财务数据更新频率分析报告

## 概述

本报告分析了AI对冲基金系统中各种财务数据类型的更新频率特性，为优化数据下载策略提供依据。

## 数据类型更新频率分析

### 1. 日更新数据类型（高频数据）

#### 1.1 股价数据 (prices)
- **更新频率**: 每个交易日更新
- **数据特性**: 实时市场数据，包含开盘价、收盘价、最高价、最低价、成交量
- **API端点**: `https://api.financialdatasets.ai/prices/`
- **建议下载策略**: 每日下载，覆盖完整交易日
- **存储格式**: 按时间范围存储（如 `AAPL_prices_2025-01-01_to_2025-06-01.json`）

#### 1.2 市值数据 (market_cap)
- **更新频率**: 每个交易日更新
- **数据特性**: 基于当前股价和流通股数计算的实时市值
- **API端点**: `https://api.financialdatasets.ai/company/facts/` (当日) 或通过financial_metrics获取
- **建议下载策略**: 每日下载，与股价数据同步
- **存储格式**: 按日期存储（如 `AAPL_market_cap_2025-01-01.json`）

#### 1.3 公司新闻数据 (company_news)
- **更新频率**: 每日更新，甚至实时更新
- **数据特性**: 公司相关新闻、公告、分析师报告等
- **API端点**: `https://api.financialdatasets.ai/company/news/`
- **建议下载策略**: 每日下载，确保获取最新新闻
- **存储格式**: 按日期存储（如 `AAPL_company_news_2025-01-01.json`）

### 2. 季度更新数据类型（低频数据）

#### 2.1 财务指标数据 (financial_metrics)
- **更新频率**: 每季度更新（财报发布后）
- **数据特性**: TTM（过去12个月）财务指标，包括PE比率、ROE、毛利率等
- **API端点**: `https://api.financialdatasets.ai/financial-metrics/`
- **建议下载策略**: 每月下载一次即可，或在财报季加密下载
- **存储格式**: 按日期存储，但数据内容相对稳定

#### 2.2 财务科目数据 (line_items)
- **更新频率**: 每季度更新（财报发布后）
- **数据特性**: 具体的财务报表科目，如收入、净利润、总资产等
- **API端点**: `https://api.financialdatasets.ai/financials/search/line-items`
- **建议下载策略**: 每月下载一次，重点关注财报发布期
- **存储格式**: 按日期存储

### 3. 不定期更新数据类型

#### 3.1 内幕交易数据 (insider_trades)
- **更新频率**: 不定期更新（基于SEC申报）
- **数据特性**: 公司内部人员的股票交易记录
- **API端点**: `https://api.financialdatasets.ai/insider-trades/`
- **建议下载策略**: 每周下载一次，确保捕获新的交易记录
- **存储格式**: 按日期存储

## 当前下载策略问题

### 问题分析
1. **过度下载季度数据**: 当前脚本对financial_metrics和line_items每周下载，造成不必要的API调用
2. **日更新数据覆盖不足**: 股价和市值数据应该每日下载，但当前按周采样可能遗漏重要交易日
3. **统一采样频率**: 所有数据类型使用相同的7天间隔，未考虑数据特性差异

### 优化建议

#### 1. 差异化下载频率
- **日更新数据**: prices, market_cap, company_news → 每日下载
- **季度更新数据**: financial_metrics, line_items → 每月下载
- **不定期更新数据**: insider_trades → 每周下载

#### 2. 智能下载策略
- **财报季加密**: 在财报发布期（1月、4月、7月、10月）增加financial_metrics和line_items的下载频率
- **交易日过滤**: 只在交易日下载市场数据，跳过周末和节假日
- **增量更新**: 检查已有数据，只下载缺失的日期

#### 3. API调用优化
- **批量下载**: 对于历史数据，使用时间范围API减少调用次数
- **缓存策略**: 季度数据在同一季度内复用，避免重复下载
- **错误重试**: 针对不同数据类型设置不同的重试策略

## 实施计划

### 阶段1: 修改下载脚本
1. 实现差异化日期生成逻辑
2. 为每种数据类型设置独立的下载频率
3. 添加交易日历支持

### 阶段2: 优化API调用
1. 修复line_items API参数问题
2. 实现智能缓存策略
3. 添加财报季检测

### 阶段3: 性能优化
1. 实现增量更新
2. 添加数据验证和完整性检查
3. 优化并发下载策略

## 预期效果

- **API调用减少**: 预计减少60-70%的不必要API调用
- **数据完整性提升**: 日更新数据覆盖所有交易日
- **下载效率提升**: 差异化策略减少总下载时间
- **成本优化**: 减少API使用成本，提高配额利用效率
